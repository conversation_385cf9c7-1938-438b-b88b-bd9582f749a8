<?php

namespace Tasawk\Filament\Resources\Subscription;

use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\RepeatableEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\SpatieMediaLibraryImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification as FilamentNotification;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\RelationManagers\RelationGroup;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Tasawk\Enum\Catalog\OptionTypes;
use Tasawk\Enum\InbodyType;
use Tasawk\Enum\NormalSubscriptionStatus;
use Tasawk\Enum\ScheduledSubscriptionStatus;
use Tasawk\Enum\SubscriptionStatus;
use Tasawk\Enum\SubscriptionType;
use Tasawk\Filament\Resources\Subscription\SubscriptionResource\Pages;
use Tasawk\Filament\Resources\Subscription\SubscriptionResource\RelationManagers\DivideCaloriesRelationManager;
use Tasawk\Filament\Resources\Subscription\SubscriptionResource\RelationManagers\ProgramsRelationManager;
use Tasawk\Models\Catalog\Option;
use Tasawk\Models\Subscription\Question;
use Tasawk\Models\Subscription\Subscription;
use Tasawk\Models\Subscription\Value;
use Tasawk\Traits\Filament\HasTranslationLabel;

class SubscriptionResource extends Resource implements HasShieldPermissions
{
    use HasTranslationLabel, Translatable;

    protected static ?string $model = Subscription::class;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-check';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->modifyQueryUsing(fn($query) => $query->where('payment_status', 'paid'))
            ->columns([
                TextColumn::make('index')->rowIndex(),
                TextColumn::make('id'),
                TextColumn::make('subscription_number')
                    ->copyable()
                    ->searchable(query: fn($query, $search) => $query->where('id', '=', substr($search, 1))->orWhere('id', $search)),
                TextColumn::make('created_at')->label(__('forms.fields.create_date'))->date(),
                TextColumn::make('status')
                    ->badge()
                    ->color(fn($state) => $state->getColor())
                    ->formatStateUsing(function ($record) {
                        return $record->status->getLabel();
                    })
                    ->searchable(),
                TextColumn::make('start_date')
                    ->formatStateUsing(function ($record) {
                        return ($record?->programs()->count() == 0 && $record->type == SubscriptionType::NORMAL) ? (__('forms.fields.not_available')) : $record?->start_date?->format('Y-m-d');
                    }),
                TextColumn::make('end_date')
                    ->formatStateUsing(function ($record) {
                        return ($record?->programs()->count() == 0 && $record->type == SubscriptionType::NORMAL) ? (__('forms.fields.not_available')) : $record?->end_date?->format('Y-m-d');
                    }),
                TextColumn::make('customer.name'),
                TextColumn::make('customer.phone')->label(__('forms.fields.phone')),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                Filter::make('ID')
                    ->label(__('forms.fields.subscription_number'))
                    ->form([
                        TextInput::make('id'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when($data['id'], fn(Builder $query, $date): Builder => $query->where('id', substr($data['id'], 1))->orWhere('id', $data['id']));
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (! $data['id']) {
                            return null;
                        }

                        return __('forms.fields.id') . ' ' . $data['id'];
                    }),
                SelectFilter::make('status')
                    ->options(SubscriptionStatus::class),
                Filter::make('date')
                    ->label(__('forms.fields.date'))
                    ->form([
                        DatePicker::make('created_from')
                            ->label(__('forms.fields.date_from')),
                        DatePicker::make('created_until')
                            ->label(__('forms.fields.date_until')),
                    ])->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['created_from'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '>=', $date),
                            )
                            ->when(
                                $data['created_until'],
                                fn(Builder $query, $date): Builder => $query->whereDate('created_at', '<=', $date),
                            );
                    }),
            ])
            ->actions([
                ActionGroup::make([

                    Action::make('change-status')
                        ->label(__('forms.fields.change_status'))
                        ->icon('heroicon-o-bolt')
                        ->color('primary')
                        ->form([
                            Select::make('status')
                                ->formatStateUsing(fn($record) => $record->status->value)
                                ->options(fn($record) => $record->type == SubscriptionType::NORMAL ? NormalSubscriptionStatus::class : ScheduledSubscriptionStatus::class),
                        ])
                        ->visible(fn(Subscription $record) => ! in_array($record->status, [SubscriptionStatus::CANCELED, SubscriptionStatus::EXPIRED]))
                        ->action(function (array $data, Subscription $record, $action): void {
                            $record->update(['status' => $data['status']]);
                            $record->StatusHistories()->create([
                                'status' => $data['status'],
                                'subscription_id' => $record->id,
                            ]);
                            FilamentNotification::make()
                                ->success()
                                ->title(__('panel.messages.success'))
                                ->body(__('panel.messages.change_status'))
                                ->persistent()
                                ->send();
                        }),
                    Action::make('assign-program')
                        ->label(__('forms.fields.assign-program'))
                        ->icon('heroicon-o-plus-circle')
                        ->color('success')
                        ->url(fn($record) => SubscriptionResource::getUrl('assign-program', [$record->id]))
                        ->modalWidth('7xl')
                        ->visible(
                            fn(Subscription $record) => ! in_array($record->status, [SubscriptionStatus::CANCELED, SubscriptionStatus::EXPIRED])&& count($record->carbs()->get()) > 0
                        ),
                    Action::make('show_invoice')
                        ->label(__('forms.fields.show_invoice'))
                        ->icon('heroicon-o-eye')
                        ->color('danger')
                        ->url(fn(Model $record) => $record?->payment_data['invoiceURL'], '_blank')
                        ->visible(fn($record) => $record?->payment_data != null),
                    Action::make('download_invoice')
                        ->label(__('forms.fields.download_invoice'))
                        ->icon('heroicon-o-arrow-down-tray')
                        ->color('success')
                        ->action(function (array $data, Subscription $record, $action) {
                            return redirect()->to(route('subscriptions.invoice.download', $record));
                        })
                        ->visible(fn($record) => $record?->payment_status->value == 'paid'),
                    Tables\Actions\ViewAction::make(),
                    // Tables\Actions\EditAction::make(),
                    // Tables\Actions\DeleteAction::make(),
                    // Tables\Actions\ForceDeleteAction::make(),
                    // Tables\Actions\RestoreAction::make(),
                ])->label(__('panel.actions.actions'))
                    ->color('primary')
                    ->button(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                    // Tables\Actions\ForceDeleteBulkAction::make(),
                    // Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationGroup::make(__('sections.programs'), [
                ProgramsRelationManager::class,
            ]),
            RelationGroup::make(__('sections.divide_calories'), [
                DivideCaloriesRelationManager::class,
            ]),
        ];
    }

    public static function getPages(): array
    {
        return [
            'view' => Pages\ViewSubscription::route('/{record}'),
            'index' => Pages\ListSubscriptions::route('/'),
            'create' => Pages\CreateSubscription::route('/create'),
            'edit' => Pages\EditSubscription::route('/{record}/edit'),
            'assign-program' => Pages\AssignProgram::route('/{record}/assign-program'),
        ];
    }

    public static function getPermissionPrefixes(): array
    {
        return [
            'view_any',
            'view',
            // 'create',
            // 'update',
            // 'delete',
            // 'delete_any',
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getNavigationGroup(): ?string
    {
        return __('menu.subscriptions');
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::where('payment_status', 'paid')->count();
    }

    public static function infolist(Infolist $infolist): Infolist
    {

        return $infolist
            ->schema([
                Grid::make()->schema([
                    Section::make('basic_information')
                        ->schema([
                            TextEntry::make('id'),
                            TextEntry::make('subscription_number'),
                            TextEntry::make('created_at')->label(__('forms.fields.create_date')),
                            TextEntry::make('status')
                                ->badge()
                                ->color(fn($state) => $state->getColor()),
                            TextEntry::make('StatusHistories.created_at')
                                ->date('Y-m-d')
                                ->state(function (Subscription $record) {
                                    return $record->StatusHistories()->orderBy('created_at', 'desc')->first() ? $record->StatusHistories()->orderBy('created_at', 'desc')->first()->created_at : $record->created_at;
                                })
                                ->label(__('forms.fields.date_status')),
                            TextEntry::make('plan.name'),
                            TextEntry::make('type')->badge(),
                            TextEntry::make('customer.name'),
                            TextEntry::make('customer.phone')->label(__('forms.fields.phone')),
                            TextEntry::make('start_date')->state(function (Subscription $record) {
                                return ($record?->programs()->count() == 0 && $record->type == SubscriptionType::NORMAL) ? (__('forms.fields.not_available')) : $record?->start_date?->format('Y-m-d');
                            }),
                            TextEntry::make('end_date')->state(function (Subscription $record) {
                                return ($record?->programs()->count() == 0 && $record->type == SubscriptionType::NORMAL) ? (__('forms.fields.not_available')) : $record?->end_date?->format('Y-m-d');
                            }),
                            TextEntry::make('duration_text')->label(__('forms.fields.duration')),
                            // TextEntry::make('inbody_type')->label(__('forms.fields.inbody_type')),
                            TextEntry::make('program.name')
                                ->visible(fn(Subscription $record) => $record->program_id != null)
                                ->label(__('forms.fields.default_program')),
                            // TextEntry::make('customer.calories')
                            //     ->label(__('forms.fields.calories_description'))
                            //     ->formatStateUsing(fn($record) => $record?->customer?->calories ?? 0),
                            TextEntry::make('renew_at')
                                ->visible(fn(Subscription $record) => $record->is_renew == 1)
                                ->label(__('forms.fields.renew_at')),
                            TextEntry::make('notes')
                                ->formatStateUsing(fn($record) => $record->notes ?? '')
                                ->columnSpan(3)
                                ->label(__('forms.fields.notes')),

                            TextEntry::make('customer.excerises_calories')->label(__('forms.fields.excersie_cals')),
                            TextEntry::make('customer.program_calories')->label(__('forms.fields.program_cals')),
                            TextEntry::make('customer.calories')->label(__('forms.fields.total_cals')),
                        ])->columns(3),
                ])->columns(3)->columnSpan(3),
                Grid::make()->schema([
                    Section::make('customer_basic_info')
                        ->schema([
                            TextEntry::make('age'),
                            TextEntry::make('gender')
                                ->formatStateUsing(fn($record) => $record->gender ? __('forms.fields.' . $record->gender) : '')
                                ->label(__('forms.fields.gender')),
                            TextEntry::make('wight')
                                ->label(__('forms.fields.weight'))
                                ->formatStateUsing(fn($record) => $record->wight . ' kg'),
                            TextEntry::make('length')
                                ->formatStateUsing(fn($record) => $record->length . ' cm'),
                        ])->columns(2),
                ])
                    ->columns(3)
                    ->columnSpan(3),
                Grid::make()->schema([
                    Section::make('body_measurements')
                        ->schema([
                            TextEntry::make('chest_circumference')
                                ->formatStateUsing(fn($record) => $record->chest_circumference . ' cm'),
                            TextEntry::make('lower_chest')
                                ->formatStateUsing(fn($record) => $record->lower_chest . ' cm'),
                            TextEntry::make('waist')
                                ->formatStateUsing(fn($record) => $record->waist . ' cm'),
                            TextEntry::make('abdomen')
                                ->formatStateUsing(fn($record) => $record->abdomen . ' cm'),
                            TextEntry::make('buttocks')
                                ->formatStateUsing(fn($record) => $record->buttocks . ' cm'),
                            TextEntry::make('upper_arm')
                                ->formatStateUsing(fn($record) => $record->upper_arm . ' cm'),
                            TextEntry::make('thigh')
                                ->formatStateUsing(fn($record) => $record->thigh . ' cm'),
                            TextEntry::make('calf')
                                ->formatStateUsing(fn($record) => $record->calf . ' cm'),
                        ])->columns(3),
                ])->visible(fn(Subscription $record) => $record->inbody_type == InbodyType::TRADITIONAL_WAY &&  $record->inbody_type != null)
                    ->columns(3)
                    ->columnSpan(3),
                Grid::make()->schema([
                    Section::make('body_measurements')
                        ->schema([
                            SpatieMediaLibraryImageEntry::make('images')
                                ->label('')
                                ->collection('images'),
                        ])->columns(3),
                ])->visible(fn(Subscription $record) => $record->inbody_type == InbodyType::INBODY_CHECK  &&  $record->inbody_type != null)
                    ->columns(3)
                    ->columnSpan(3),
                Grid::make()
                    ->schema([
                        Section::make('body_images')
                            ->schema([
                                SpatieMediaLibraryImageEntry::make('body_images')
                                    ->label('')
                                    ->columns(3)
                                    ->collection('body_images'),

                            ])
                            ->columns(3),
                    ])->visible(fn(Subscription $record) => count($record->getMedia('body_images'))  > 0)
                    ->columnSpan(3),

                Grid::make()
                    ->schema([
                        Section::make('health_information')
                            ->schema([
                                RepeatableEntry::make('Questions')
                                    ->label('')
                                    ->schema([
                                        TextEntry::make('question.name')->label(''),
                                        RepeatableEntry::make('values')
                                            ->label('')
                                            ->schema([
                                                TextEntry::make('value_id')
                                                    ->formatStateUsing(function ($record) {
                                                        $question_id = $record?->subscription_question?->question_id;
                                                        $question = Question::where('id', $question_id)->first();
                                                        if ($question->type == OptionTypes::RADIO || $question->type == OptionTypes::CHECKBOX) {
                                                            $va = Value::find($record->value_id);
                                                            $value = $va->value;
                                                        } else {
                                                            $value = $record->value_id;
                                                        }

                                                        return $value ?? '';
                                                    })
                                                    ->label(''),
                                            ])->grid(2),
                                    ])->grid(2),
                            ])->visible(fn(Subscription $record) => $record->questions()->count() > 0)
                    ])->columnSpan(3),
                Grid::make()
                    ->schema([
                        Section::make('cost_detailes')->schema([
                            TextEntry::make('subtotal')->label(__('forms.fields.subTotal'))->state(function (Subscription $subscription) {
                                return $subscription->cart_details[0]['subtotal'];
                            }),
                            TextEntry::make('coupon_discount')->label(__('forms.fields.coupon_discount'))->state(function (Subscription $subscription) {
                                return $subscription->cart_details[0]['coupon_discount'];
                            })->visible(fn(Subscription $subscription) => $subscription->coupon_id != null),
                            TextEntry::make('total')->label(__('forms.fields.total'))->state(function (Subscription $subscription) {
                                return $subscription->total;
                            }),
                            TextEntry::make('payment_method')->label(__('forms.fields.payment_method'))->state(function (Subscription $subscription) {
                                return $subscription?->payment_data['method'];
                            }),
                            TextEntry::make('payment_status')
                                ->columnSpan(fn(Subscription $subscription) => $subscription->coupon_id != null ? 2 : 1),
                            TextEntry::make('invoice_url')
                                ->label(__('forms.fields.invoice_url'))
                                ->url(fn(Subscription $subscription) => $subscription?->payment_data['invoiceURL'], '__blank')
                                ->color('primary')
                                ->state(function (Subscription $subscription) {
                                    return $subscription?->payment_data['invoiceURL'];
                                }),
                        ])->hidden(fn(Subscription $subscription) => $subscription->payment_data == null)->columns(3)
                            ->columns(3),
                    ])->columnSpan(3),
                Grid::make()
                    ->schema([
                        Section::make('cancellation_reasons')
                            ->schema([
                                TextEntry::make('cancellation.note')
                                    ->visible(fn(Subscription $record) => ($record?->cancellation?->reason_id == 0))
                                    ->label(__('forms.fields.cancellation_reason_name'))
                                    ->columnSpan(1),
                                TextEntry::make('cancellation.reason.name')
                                    ->visible(fn(Subscription $record) => ($record?->cancellation?->reason_id != 0))
                                    ->label(__('forms.fields.cancellation_reason_name'))
                                    ->columnSpan(1),
                                TextEntry::make('cancellation.created_at')
                                    ->date('Y-m-d')
                                    ->label(__('forms.fields.created_at'))
                                    ->columnSpan(2),
                            ])
                            ->visible(fn(Subscription $record) => isset($record->cancellation) && $record->cancellation)
                            ->columns(3),
                    ])->columnSpan(3),

                Grid::make()
                    ->schema([
                        Section::make('rating')
                            ->schema([
                                TextEntry::make('rate.comment')
                                    ->label(__('forms.fields.comment'))
                                    ->columnSpan(1),
                                TextEntry::make('rate.rate')
                                    ->label(__('forms.fields.rate'))
                                    ->columnSpan(1),
                                TextEntry::make('rate.created_at')
                                    ->date('Y-m-d')
                                    ->label(__('forms.fields.created_at'))
                                    ->columnSpan(1),
                            ])
                            ->visible(fn(Subscription $record) => $record->rated())
                            ->columns(3),
                    ])->columnSpan(3),
            ]);
    }
}
