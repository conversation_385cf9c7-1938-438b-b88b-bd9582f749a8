<?php

namespace Tasawk\Http\Requests\Api\Customer;

use Illuminate\Foundation\Http\FormRequest;
use Tasawk\Rules\KSAPhoneRule;

class ContactUsRequest extends FormRequest {

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize() {
        return true;
    }

    public function rules() {
        return [

            "title" => ['required', 'string', 'max:150'],
            "message" => ["required", "min:25"],
            'subject' => [],
            "contact_type_id" => ["required", "exists:contact_types,id"],
        ];
    }

    protected function prepareForValidation() {

        $this->merge([
            "subject" => '',
            'user_id' => !$this->boolean('guest') ? request()->user('sanctum')?->id : null,
        ]);
    }

//    public function attributes() {
//        return [
//            `name` => __('name'),
//            `email` => __('email'),
//            `phone` => __('phone'),
//            `title` => __('title'),
//            'message' => __('validation.attributes.message'),
//            'subject' => __('subject'),
//            'contact_type_id' => __('validation.attributes.contact_type_id'),
//        ];
//    }
}
