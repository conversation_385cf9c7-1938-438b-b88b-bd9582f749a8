<?php

namespace Tasawk\Filament\Resources\Subscription\SubscriptionResource\Pages;

use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Tasawk\Enum\SubscriptionStatus;
use Tasawk\Filament\Resources\Subscription\SubscriptionResource;

class ListSubscriptions extends ListRecords
{
    protected static string $resource = SubscriptionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        return [
            __('panel.enums.waiting') => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $query->whereIn('status', [SubscriptionStatus::PROCESSING, SubscriptionStatus::WAITING])
                    ->where('program_id', '=', null)
                ),
            __('panel.enums.activate') => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $query->whereIn('status', [SubscriptionStatus::INREVIEW, SubscriptionStatus::WAITING])
                ),
            __('panel.enums.expire') => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $query->whereIn('status', [SubscriptionStatus::EXPIRED])),
            __('panel.enums.cancel') => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $query->whereIn('status', [SubscriptionStatus::CANCELED])),

        ];
    }
}
