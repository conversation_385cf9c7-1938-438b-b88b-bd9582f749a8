<?php

namespace Tasawk\Actions\Shared\Authentication;

use Tasawk\Lib\Utils;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Mail;
use Tasawk\Api\V1\Customer\SmsService;
use Lorisleiva\Actions\Concerns\AsAction;
use Tasawk\Lib\NotificationMessageParser;
use Tasawk\Notifications\OTPCodeSentNotification;
use Tasawk\Mail\VerifyPhoneMail;

class SendVerificationCode {
    use AsAction;

    public function handle($user, $phone = null)
    {
        $code = Utils::randomOtpCode();

        $phoneNumber = Str::replace(' ', '', $phone ?? $user->phone);

        $user->verificationCodes()->create([
            'phone' => $phoneNumber,
            'code'  => $code,
        ]);

        dispatch(function () use ($user, $phoneNumber, $code) {
            $message = NotificationMessageParser::init($user)
                ->customerMessage("CODE", ['CODE' => $code])
                ->parse();

            $text = $message[app()->getLocale()] ?? $message['en'];

            // Check if user is from Saudi Arabia (country code 966)
            $isSaudiArabia = $this->isSaudiArabiaUser($phoneNumber);

            if ($isSaudiArabia) {
                // Send SMS for Saudi Arabia users
                $smsService = app(abstract: SmsService::class);
                $smsService->sendOtp($phoneNumber, $code);
            } else {
                // Send email for non-Saudi Arabia users
                if ($user->email) {
                    Mail::to($user->email)->send(new VerifyPhoneMail($text));
                }
            }

        })->afterResponse();
    }

    /**
     * Check if the user is from Saudi Arabia based on phone number
     *
     * @param string $phoneNumber
     * @return bool
     */
    private function isSaudiArabiaUser($phoneNumber)
    {
        // Remove any spaces and special characters
        $cleanPhone = Str::replace([' ', '+', '-'], '', $phoneNumber);

        // Check if phone starts with 966 (Saudi Arabia country code)
        return Str::startsWith($cleanPhone, '966');
    }

}
