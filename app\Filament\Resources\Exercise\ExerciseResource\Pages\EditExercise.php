<?php

namespace Tasawk\Filament\Resources\Exercise\ExerciseResource\Pages;

use Tasawk\Filament\Resources\Exercise\ExerciseResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditExercise extends EditRecord
{
    use EditRecord\Concerns\Translatable;
    protected static string $resource = ExerciseResource::class;

    protected function getHeaderActions(): array {
        return [
            Actions\DeleteAction::make(),
            Actions\LocaleSwitcher::make(),

        ];
    }

    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }
}
