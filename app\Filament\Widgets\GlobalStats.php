<?php

namespace Tasawk\Filament\Widgets;

use <PERSON><PERSON><PERSON><PERSON><PERSON>h\FilamentShield\Traits\HasWidgetShield;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Tasawk\Enum\OrderStatus;
use Tasawk\Enum\SubscriptionStatus;
use Tasawk\Enum\WorkerOrderStatus;
use Tasawk\Models\Catalog\Product;
use Tasawk\Models\Content\Contact;
use Tasawk\Models\Customer;
use Tasawk\Models\Exercise\Exercise;
use Tasawk\Models\Meal\Meal;
use Tasawk\Models\Order;
use Tasawk\Models\Program\Program;
use Tasawk\Models\Subscription\Subscription;
use Tasawk\Models\Worker;
use Tasawk\Models\Zone;

class GlobalStats extends BaseWidget
{
    use HasWidgetShield;

    protected function getStats(): array
    {
        return [
            Stat::make(__('panel.stats.customers_count'), Customer::count()),
            Stat::make(__('panel.stats.subscribers_customers'), Customer::whereHas('subscriptions')->count()),
            Stat::make(__('panel.stats.not_subscribers_customers'), Customer::whereDoesntHave('subscriptions')->count()),
            Stat::make(__('panel.stats.total_subscriptions'), 'SAR '. number_format(Subscription::where('payment_status','paid')->sum('total'),2)),
            Stat::make(__('panel.stats.subscriptions_count'), Subscription::where('payment_status','paid')->count()),
            Stat::make(__('panel.stats.new_subscriptions_count'), Subscription::where('payment_status','paid')
            ->whereDoesntHave('programs')->count()),
            Stat::make(__('panel.stats.total_resend_subscriptions'), Subscription::where('payment_status','paid')
            ->whereHas('programs', function($query){
                $query->where('end_date','<' ,Carbon::now());
            })
            ->whereIn('status',[SubscriptionStatus::WAITING,SubscriptionStatus::INREVIEW])->count()),
            Stat::make(__('panel.stats.active_subscriptions_count'), Subscription::where('payment_status','paid')
            ->whereIn('status',[SubscriptionStatus::WAITING,SubscriptionStatus::INREVIEW])->count()),
            Stat::make(__('panel.stats.expired_subscriptions_count'), Subscription::where('payment_status','paid')
            ->whereIn('status',[SubscriptionStatus::EXPIRED])->count()),
            Stat::make(__('panel.stats.canceled_subscriptions_count'), Subscription::where('payment_status','paid')
            ->whereIn('status',[SubscriptionStatus::CANCELED])->count()),
            Stat::make(__('panel.stats.cancel_total_subscriptions'), 'SAR '. number_format(Subscription::where('payment_status','paid')
            ->whereIn('status',[SubscriptionStatus::CANCELED])
            ->sum('total'),2)),
            Stat::make(__('panel.stats.products_count'), Product::count()),
            Stat::make(__('panel.stats.meals_count'), Meal::count()),
            Stat::make(__('panel.stats.exercises_count'), Exercise::count()),
            Stat::make(__('panel.stats.programes_count'), Program::count()),
            Stat::make(__('panel.stats.contacts_count'), Contact::count()),
        ];
    }
}