<?php

namespace Tasawk\Http\Resources\Api\Customer\Subscriptions;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Tasawk\Models\Catalog\Option;

class MealResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'image' => $this->meal?->getFirstMediaUrl(),
            'name' => $this?->meal?->name,
            'category' => $this?->meal?->category?->name,
            'notes' => $this?->meal?->notes_text,
            'products' => MealProductsResource::collection($this?->products),
            'sum_of_protien' => $this->sum_of_protien.' '.Option::where('id', '2')->first()?->unit?->name,
            'sum_of_calories' => $this->sum_of_calories.' '.Option::where('id', '1')->first()?->unit?->name,
            'sum_of_fat' => $this->sum_of_fat.' '.Option::where('id', '4')->first()?->unit?->name,
            'sum_of_carbohydrate' => $this->sum_of_carbohydrate.' '.Option::where('id', '3')->first()?->unit?->name,
            'sum_of_sugars' => $this->sum_of_sugars.' '.Option::where('id', '5')->first()?->unit?->name,

        ];
    }
}