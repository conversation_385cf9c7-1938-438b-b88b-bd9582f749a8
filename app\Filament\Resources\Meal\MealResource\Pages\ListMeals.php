<?php

namespace Tasawk\Filament\Resources\Meal\MealResource\Pages;

use Tasawk\Filament\Resources\Meal\MealResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListMeals extends ListRecords
{
    protected static string $resource = MealResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
