<?php

namespace Tasawk\Filament\Resources\Exercise;

use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Filament\Forms;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Tasawk\Enum\ModelStatus;
use Tasawk\Filament\Resources\Exercise\ExerciseResource\Pages;
use Tasawk\Models\Exercise\CategoryExercise;
use Tasawk\Models\Exercise\Exercise;
use Tasawk\Traits\Filament\HasTranslationLabel;

class ExerciseResource extends Resource implements HasShieldPermissions
{
    use HasTranslationLabel;
    use Translatable;

    protected static ?string $model = Exercise::class;

    protected static ?string $navigationIcon = 'heroicon-o-bolt';

    protected static ?int $navigationSort = 2;
    //    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()->schema([
                    Forms\Components\Section::make('general')->schema([
                        TextInput::make('name')
                            ->required()
                            ->columnSpan(2)
                            ->translateLabel(),
                        Forms\Components\RichEditor::make('description')
                            ->required()
                            ->columnSpan(2)
                            ->translateLabel(),
                        Select::make('category_id')
                            ->required()
                            ->columnSpan(1)
                            ->options(CategoryExercise::enabled()->pluck('name', 'id'))
                            ->getOptionLabelFromRecordUsing(fn (CategoryExercise $record) => "{$record->getTranslation('name', 'en')} - {$record->getTranslation('name', 'ar')}")
                            ->searchable(['name->ar', 'name->en']),

                        Forms\Components\TextInput::make('number_of_session')
                            ->type('number')
                            ->suffix(__('forms.fields.session'))
                            ->columnSpan(1)
                            ->numeric()
                            ->required(),
                        Forms\Components\TextInput::make('number_of_repetition')
                            ->type('number')
                            ->suffix(__('forms.fields.time'))
                            ->columnSpan(1)
                            ->numeric()
                            ->required(),
                        Forms\Components\TextInput::make('rest')
                            ->columnSpan(1)
                            ->suffix(__('forms.fields.minute'))
                            ->type('number')
                            ->required(),
                        Toggle::make('status')->default(1)
                            ->label(__('forms.fields.enabled'))
                            ->onColor('success')
                            ->offColor('danger')
                            ->translateLabel(),
                    ])->columnSpan(2),
                ])->columnSpan(3),
                Forms\Components\Group::make()->schema([
                    Forms\Components\Section::make('media')
                        ->schema([
                            SpatieMediaLibraryFileUpload::make('image')
                                ->required(),
                            SpatieMediaLibraryFileUpload::make('video')
                                ->required()
                                ->preserveFilenames()
                                ->collection('video')
                                ->maxSize(200000),
                        ]),
                    Forms\Components\Section::make('notes')
                        ->schema([
                            Repeater::make('notes')
                                ->label(__('forms.fields.notes'))
                                ->schema([
                                    TextInput::make('note')
                                        ->label(__('forms.fields.note'))
                                        ->required(),
                                ])->defaultItems(1),
                        ]),
                ])->columnSpan(2),
            ])->columns(5);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('index')->rowIndex(),
                TextColumn::make('id'),
                SpatieMediaLibraryImageColumn::make('image')
                ->getStateUsing(fn (Exercise $record)=> $record->getFirstMediaUrl('default') ?? ''),
                TextColumn::make('name')->searchable(),
                TextColumn::make('category.name')->searchable(),
                IconColumn::make('status')
                    ->boolean()
                    ->action(
                        Action::make('Active')
                            ->label(fn (Exercise $record): string => $record->status ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            // ->disabled(fn (Model $record): bool => ! auth()->user()->can('update', $record))
                            ->requiresConfirmation()
                            ->action(fn (Exercise $record) => $record->toggleStatus())
                    ),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                SelectFilter::make('status')
                    ->options(ModelStatus::class),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListExercises::route('/'),
            'create' => Pages\CreateExercise::route('/create'),
            'edit' => Pages\EditExercise::route('/{record}/edit'),
        ];
    }

    public static function getNavigationGroup(): ?string
    {
        return __('menu.exercises');
    }

    public static function getPermissionPrefixes(): array
    {
        return [
            'view_any',
            'create',
            'update',
            'delete',
            'delete_any',
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
