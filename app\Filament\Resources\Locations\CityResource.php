<?php

namespace Tasawk\Filament\Resources\Locations;

use Closure;
use Filament\Forms;
use Filament\Tables;
use Tasawk\Models\City;
use Tasawk\Models\Zone;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Support\Str;
use Tasawk\Models\Delegate;
use Tasawk\Enum\ModelStatus;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables\Actions\Action;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Infolists\Components\Grid;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Illuminate\Database\Eloquent\Model;
use Filament\Forms\Components\TextInput;
use Tasawk\Filament\Resources\Locations;
use Filament\Tables\Filters\SelectFilter;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Resources\Concerns\Translatable;
use Cheesegrits\FilamentGoogleMaps\Fields\Map;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Tasawk\Traits\Filament\HasTranslationLabel;
use MatanYadaev\EloquentSpatial\Objects\Geometry;
use Filament\Resources\RelationManagers\RelationGroup;
use Tasawk\Filament\Resources\Employees\DelegateResource;
use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Tasawk\Filament\Resources\Locations\CityResource\RelationManagers\DelegatesRelationManager;
use Tasawk\Filament\Resources\Locations\CityResource\RelationManagers\DriversRelationManager;

class CityResource extends Resource  implements HasShieldPermissions
{
    use Translatable;
    use HasTranslationLabel;

    protected static ?string $model = City::class;

    protected static ?string $navigationIcon = 'heroicon-o-map-pin';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('basic_information')
                    ->schema([
                        TextInput::make('name')->required(),
                        Select::make('zone_id')
                            ->label(__('forms.fields.zone_name'))
                            ->required()
                            ->options(Zone::pluck('name', 'id'))
                            ->searchable(['name->ar', 'name->en']),
                        Toggle::make('status')->default(1)
                            ->onColor('success')
                            ->offColor('danger')
                    ])

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('index')->rowIndex(),
                TextColumn::make('name')->searchable(),
                TextColumn::make('customers_count')
                    ->label(__('forms.fields.customers_count')),
                TextColumn::make('zone.name'),
                IconColumn::make('status')
                    ->boolean()
                    ->action(
                        Action::make('Active')
                            ->label(fn (City $record): string => $record->status ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            ->disabled(fn (Model $record): bool => !auth()->user()->can('update', $record))
                            ->requiresConfirmation()
                            ->action(fn (City $record) => $record->toggleStatus())

                    ),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options(ModelStatus::class)
            ])
            ->actions([

                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()


            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    static public function infolist(infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Grid::make()->schema([
                    Section::make("basic_information")
                        ->schema([
                            TextEntry::make('id'),
                            TextEntry::make('name'),
                            TextEntry::make('zone.name'),
                            TextEntry::make('status')->badge(),
                        ])->columns(2),

                ])->columns(2)
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // RelationGroup::make(__('sections.delegates_list'), [
            //     DelegatesRelationManager::class,
            // ]),
            // RelationGroup::make(__('sections.drivers_list'), [
            //     DriversRelationManager::class,
            // ]),

        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Locations\CityResource\Pages\ListCities::route('/'),
            'create' => Locations\CityResource\Pages\CreateCity::route('/create'),
            'edit' => Locations\CityResource\Pages\EditCity::route('/{record}/edit'),
            'view' => Locations\CityResource\Pages\ViewCity::route('/{record}'),
        ];
    }

    public static function getNavigationGroup(): ?string
    {
        return __('menu.locations');
    }
    public static function getPermissionPrefixes(): array
    {
        return [
            'view_any',
            // 'view',
            'create',
            'update',
            'delete',
            'delete_any',
        ];
    }
}