<?php

namespace Tasawk\Filament\Resources\Subscription\SubscriptionResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Tasawk\Models\Catalog\Option;
use Tasawk\Models\Exercise\Exercise;
use Tasawk\Models\Meal\Meal;
use Tasawk\Models\NutritionalSupplement;
use Tasawk\Models\Program\Program;

class DivideCaloriesRelationManager extends RelationManager
{
    protected static string $relationship = 'carbs';

    public static function getTitle(Model $ownerRecord, string $pageClass): string
    {
        return __('sections.divide_calories');
    }

    public static function getModelLabel(): ?string
    {
        return __('sections.divide_calories');
    }

    public static function getFooterLabel(): ?string
    {
        return __('sections.divide_calories');
    }

    public static function getPluralModelLabel(): string
    {
        return __('sections.divide_calories');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([

            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->recordTitleAttribute('id')
            ->heading(__('sections.divide_calories'))
            ->columns([
                TextColumn::make('index')->rowIndex(),
                TextColumn::make('option.name')
                
                    ->searchable(),
                TextColumn::make('percentage')
                    ->formatStateUsing(fn($record) => $record->percentage. ' %')
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                // Tables\Actions\CreateAction::make(),
            ])
            ->actions([


            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

}
