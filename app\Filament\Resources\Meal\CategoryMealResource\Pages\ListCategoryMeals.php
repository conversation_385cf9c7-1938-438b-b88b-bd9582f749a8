<?php

namespace Tasawk\Filament\Resources\Meal\CategoryMealResource\Pages;

use Tasawk\Filament\Resources\Meal\CategoryMealResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCategoryMeals extends ListRecords
{
    protected static string $resource = CategoryMealResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
