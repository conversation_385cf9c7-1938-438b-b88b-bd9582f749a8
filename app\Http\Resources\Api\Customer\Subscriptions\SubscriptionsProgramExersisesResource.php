<?php

namespace Tasawk\Http\Resources\Api\Customer\Subscriptions;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SubscriptionsProgramExersisesResource extends JsonResource
{
    protected $index;

    public function __construct($resource, $index = null)
    {
        parent::__construct($resource);
        $this->index = $index + 1;
    }

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'index' => $this->index,
            'id' => $this?->id,
            'name' => $this?->exercise?->name,
            'category' => $this?->exercise?->category?->name,
            'image' => $this?->exercise?->getFirstMediaUrl(),
        ];
    }
}