<?php

namespace Tasawk\Filament\Resources\Subscription\QuestionResource\Pages;

use Filament\Actions;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord;
use Filament\Resources\Pages\EditRecord\Concerns\Translatable;
use Tasawk\Filament\Resources\Subscription\QuestionResource;
use Tasawk\Models\Subscription\Value;

class EditQuestion extends EditRecord
{
    use Translatable,QuestionResource\Schema\QuestionSchema;

    protected static string $resource = QuestionResource::class;

    protected function getHeaderActions(): array {

        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }

    public function form(Form $form): Form {
        return $form->schema($this->schema());
    }


    // public function afterSave()
    // {
    //     $values = $this->data['values'];
    //     foreach ($values as $key => $value) {
    //         $recordId = explode('-', $key)[1];
    //         $valAr = is_array($value['value']['ar']) ? $value['value']['ar']['ar'] : $value['value']['ar'];
    //         $valEn = is_array($value['value']['en'])? $value['value']['en']['en'] : $value['value']['en'];
    //         Value::where('id', $recordId)->update([
    //             'value' => [
    //                 'ar' => $valAr,
    //                 'en' => $valEn
    //             ],
    //         ]);
    //     }
    // }
}