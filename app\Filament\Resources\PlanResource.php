<?php

namespace Tasawk\Filament\Resources;

use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section as FormSection;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Tasawk\Forms\Components\SelectFontAwesomeIcon;
use Filament\Forms\Form;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Tasawk\Filament\Resources\PlanResource\Pages;
use Tasawk\Filament\Resources\PlanResource\RelationManagers\PricingRelationManager;
use Tasawk\Models\Plan;
use Tasawk\Traits\Filament\HasTranslationLabel;

class PlanResource extends Resource implements HasShieldPermissions
{
    use HasTranslationLabel, Translatable;

    protected static ?string $model = Plan::class;

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document-check';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                FormSection::make('basic_information')
                    ->schema([
                        SpatieMediaLibraryFileUpload::make('image')
                            ->columnSpan(['xl' => 2])
                            ->image()
                            ->collection('default'),
                        Select::make('icon_type')
                            ->label('Icon Type')
                            ->options([
                                'upload' => 'Upload Custom Icon',
                                'fontawesome' => 'Choose FontAwesome Icon'
                            ])
                            ->default('fontawesome')
                            ->live()
                            ->columnSpan(['xl' => 2])
                            ->required(),
                        SpatieMediaLibraryFileUpload::make('icon')
                            ->columnSpan(['xl' => 2])
                            ->image()
                            ->collection('icon')
                            ->label(__('forms.fields.icon'))
                            ->helperText('Upload an icon for the plan to display in packages section')
                            ->acceptedFileTypes(['image/svg+xml', 'image/png', 'image/jpeg', 'image/gif'])
                            ->visible(fn ($get) => $get('icon_type') === 'upload'),
                        SelectFontAwesomeIcon::make('fontawesome_icon')
                            ->label('FontAwesome Icon')
                            ->setMode('fitness')
                            ->columnSpan(['xl' => 2])
                            ->helperText('Choose a fitness-related FontAwesome icon for the plan')
                            ->visible(fn ($get) => $get('icon_type') === 'fontawesome')
                            ->required(fn ($get) => $get('icon_type') === 'fontawesome'),
                        TextInput::make('name')
                            ->columnSpan(['xl' => 1])
                            ->required(),
                        Textarea::make('description')
                            ->columnSpan(['xl' => 1])
                            ->required(),
                        Repeater::make('notes')
                            ->label(__('forms.fields.notes'))
                            ->columnSpan(['xl' => 2])
                            ->schema([
                                TextInput::make('note')
                                    ->label(__('forms.fields.note'))
                                    ->required(),
                            ])
                            ->defaultItems(1),
                        SpatieMediaLibraryFileUpload::make('sub_images')
                            ->columnSpan(['xl' => 2])
                            ->image()
                            ->multiple()
                            ->collection('sub_images'),

                    ])->columns(2),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('index')->rowIndex(),
                Tables\Columns\TextColumn::make('id'),
                Tables\Columns\TextColumn::make('name')->searchable(),
                SpatieMediaLibraryImageColumn::make('image')
                    ->getStateUsing(fn (Plan $record) => $record->getFirstMediaUrl('default') ?? ''),
                Tables\Columns\TextColumn::make('icon_display')
                    ->label(__('forms.fields.icon'))
                    ->html()
                    ->getStateUsing(function (Plan $record) {
                        if ($record->icon_type === 'upload' && $record->getFirstMediaUrl('icon')) {
                            return '<img src="' . $record->getFirstMediaUrl('icon') . '" style="width: 40px; height: 40px; object-fit: contain;">';
                        } elseif ($record->icon_type === 'fontawesome' && $record->fontawesome_icon) {
                            return '<i class="' . $record->fontawesome_icon . '" style="font-size: 24px;"></i>';
                        }
                        return '<i class="far fa-dumbbell" style="font-size: 24px;"></i>';
                    }),
                IconColumn::make('status')
                    ->boolean()
                    ->action(
                        Action::make('Active')
                            ->label(fn (Model $record): string => $record->status ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            ->disabled(fn (Model $record): bool => ! auth()->user()->can('update', $record))
                            ->requiresConfirmation()
                            ->action(fn (Model $record) => $record->toggleStatus())
                    ),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),

            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                \Filament\Infolists\Components\Grid::make()->schema([
                    \Filament\Infolists\Components\Section::make('basic_information')
                        ->schema([
                            //images
                            TextEntry::make('name')->label(__('forms.fields.name')),
                            TextEntry::make('description')->label(__('forms.fields.description')),
                            TextEntry::make('notes_text')->label(__('forms.fields.notes')),
                            TextEntry::make('status_text')->label(__('forms.fields.status')),
                        ])->columns(3),
                ])->columns(2),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            PricingRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPlans::route('/'),
            'create' => Pages\CreatePlan::route('/create'),
            'view' => Pages\ViewPlan::route('/{record}'),
            'edit' => Pages\EditPlan::route('/{record}/edit'),
        ];
    }

    public static function getNavigationGroup(): ?string
    {
        return __('menu.content');
    }

    public static function getPermissionPrefixes(): array
    {
        return [
            'view_any',
            'view',
            'create',
            'update',
            'delete',
            'delete_any',
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
