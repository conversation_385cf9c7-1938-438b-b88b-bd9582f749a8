<?php

namespace Tasawk\Filament\Resources\Subscription;

use <PERSON>zhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Tasawk\Enum\ModelStatus;
use Tasawk\Filament\Resources\Subscription\QuestionResource\Pages;
use Tasawk\Models\Subscription\Question;
use Tasawk\Traits\Filament\HasTranslationLabel;
use Tasawk\Filament\Resources\Subscription\QuestionResource\RelationManagers\ValuesRelationManager;

class QuestionResource extends Resource implements HasShieldPermissions
{
    use HasTranslationLabel;
    use Translatable;

    protected static ?string $model = Question::class;

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationIcon = 'heroicon-o-question-mark-circle';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
        ->columns([
            TextColumn::make('id'),
            TextColumn::make('name'),
            IconColumn::make('status')
                ->boolean()
                ->action(
                    Action::make('status')
                        ->label(fn(Question $record): string => $record->status ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                        // ->disabled(fn(Model $record): bool => !auth()->user()->can('update', $record))
                        ->requiresConfirmation()
                        ->action(fn(Question $record) => $record->toggleStatus())
                ),
        ])
        ->filters([
            SelectFilter::make('status')
                ->options(ModelStatus::class),
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // ValuesRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListQuestions::route('/'),
            'create' => Pages\CreateQuestion::route('/create'),
            'edit' => Pages\EditQuestion::route('/{record}/edit'),
        ];
    }

    public static function getNavigationGroup(): ?string
    {
        return __('menu.subscriptions');
    }

    public static function getPermissionPrefixes(): array
    {
        return [
            'view_any',
            'create',
            'update',
            'delete',
            'delete_any',
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
            SoftDeletingScope::class,
        ]);
    }
}