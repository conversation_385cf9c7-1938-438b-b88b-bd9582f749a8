<?php

namespace Tasawk\Http\Requests\Api\Customer\Profile;

use Illuminate\Foundation\Http\FormRequest;
use Tasawk\Rules\IsValidVerificationCodeRule;
use Tasawk\Rules\KSAPhoneRule;
use Tasawk\Rules\PhoneNumber;

class VerifyAltPhoneRequest extends FormRequest {

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize() {
        return true;
    }


    public function rules() {
        return [
            'code' => ['required', 'numeric','digits:4', new IsValidVerificationCodeRule()],
            'phone' => [
                'required',
                // 'exists:users',
                'regex:/^\d{8,15}$/',
                new PhoneNumber($this->input('country_code')),
            ],
            'country_code' => ['required', 'string', 'regex:/^\d{1,4}$/'],
        ];
    }

}
