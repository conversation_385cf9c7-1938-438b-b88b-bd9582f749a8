<?php

namespace Tasawk\Filament\Resources\Program\ProgramResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Tasawk\Filament\Resources\Program\ProgramResource;
use Tasawk\Models\Program\MealProgram;

class CreateProgram extends CreateRecord {
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = ProgramResource::class;

    protected function getHeaderActions(): array {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }

    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl('index');
    }


}