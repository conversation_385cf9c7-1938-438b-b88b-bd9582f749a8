<?php

namespace Tasawk\Filament\Resources\Subscription\SubscriptionResource\Pages;

use Tasawk\Filament\Resources\Subscription\SubscriptionResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditSubscription extends EditRecord
{
    protected static string $resource = SubscriptionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\DeleteAction::make(),
        ];
    }
}