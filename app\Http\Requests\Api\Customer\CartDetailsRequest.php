<?php

namespace Tasawk\Http\Requests\Api\Customer;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Tasawk\Lib\Utils;
use Tasawk\Rules\AddressBelongToAuthUserRule;
use Tasawk\Rules\IsProductAvailableInBranchRule;
use Tasawk\Rules\IsRequiredProductOptionsRepresentRule;
use Tasawk\Rules\IsValidProductOptionsRule;
use Tasawk\Rules\IsValidProductOptionValuesRule;
use Tasawk\Rules\IsValidCoupon;
use Tasawk\Rules\SufficientProductQuantity;


class CartDetailsRequest extends FormRequest {

    public function authorize() {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array {
        return [
            'plan_id' => [ 'required', 'exists:plans,id' ],
            'plan_price_id' => [ 'required', 'exists:plans_pricings,id' ],
            // 'type' => [ 'required', Rule::in([ 'normal', 'urgent' ])],
            // 'start_date' => [ 'required_if:type,urgent', 'date_format:Y-m-d'],
            // 'age' => [ 'required'],
            // 'gender' => ['required' ,'in:0,1'],
            // 'wight' => ['required'],
            // 'length' => ['required'],
            // 'wake_up_time' => ['required'],
            // 'sleep_time' => ['required'],
            'coupon_code' => ['nullable', 'exists:coupons,code', new IsValidCoupon()],
        ];
    }

}