<?php

namespace Tasawk\Filament\Resources\Subscription\QuestionResource\Pages;

use Tasawk\Filament\Resources\Subscription\QuestionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListQuestions extends ListRecords
{
    protected static string $resource = QuestionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
