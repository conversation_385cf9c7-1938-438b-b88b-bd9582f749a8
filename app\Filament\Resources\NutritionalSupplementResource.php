<?php

namespace Tasawk\Filament\Resources;

use <PERSON>zhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Filament\Forms;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Tasawk\Enum\ModelStatus;
use Tasawk\Filament\Resources\NutritionalSupplementResource\Pages;
use Tasawk\Models\Catalog\Option;
use Tasawk\Models\NutritionalSupplement;
use Tasawk\Traits\Filament\HasTranslationLabel;

class NutritionalSupplementResource extends Resource implements HasShieldPermissions
{
    use HasTranslationLabel;
    use Translatable;

    protected static ?int $navigationSort = 1;

    protected static ?string $model = NutritionalSupplement::class;

    protected static ?string $navigationIcon = 'heroicon-o-battery-100';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()->schema([
                    Forms\Components\Section::make('general')->schema([
                        TextInput::make('name')
                            ->required()
                            ->translateLabel(),
                        Textarea::make('description')
                            ->required()
                            ->translateLabel(),
                        SpatieMediaLibraryFileUpload::make('image')
                        ->label(__('forms.fields.main_image'))
                            ->required(),
                            SpatieMediaLibraryFileUpload::make('mockup')
                            ->collection('mockup')
                            ->required(),
                        TextInput::make('link')
                            ->url()
                            ->required(),
                        Toggle::make('status')->default(1)
                            ->label(__('forms.fields.enabled'))
                            ->onColor('success')
                            ->offColor('danger')
                            ->translateLabel(),
                    ])
                ])->columnSpan(3),
                Forms\Components\Group::make()->schema([
                    Forms\Components\Section::make('notes')
                        ->schema([
                            Repeater::make('notes')
                                ->label(__('forms.fields.notes'))
                                ->schema([
                                    TextInput::make('note')
                                        ->label(__('forms.fields.note'))
                                        ->required(),
                                ])->required()->defaultItems(1),
                        ]),
                    Forms\Components\Section::make('options')
                        ->schema(static::productOptions()),
                ])->columnSpan(2),
            ])->columns(5);
    }

    public static function productOptions()
    {
        $schema = [];
        foreach (Option::latest()->where('id','!=',5)->get() as $option) {
            $schema[] = TextInput::make("options.{$option->id}")
                ->required()
                ->formatStateUsing(fn ($record) => optional($record?->options?->where('id', $option?->id)?->first()?->pivot)?->value ?? '')
                ->label($option->name)
                ->suffix($option?->unit?->name);
        }

        return $schema;
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('index')->rowIndex(),
                TextColumn::make('id'),
                SpatieMediaLibraryImageColumn::make('image')
                ->getStateUsing(fn (NutritionalSupplement $record) => $record->getFirstMediaUrl('default') ?? ''),
                TextColumn::make('name'),
                IconColumn::make('status')
                    ->boolean()
                    ->action(
                        Action::make('Active')
                            ->label(fn (NutritionalSupplement $record): string => $record->status ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            // ->disabled(fn (Model $record): bool => ! auth()->user()->can('update', $record))
                            ->requiresConfirmation()
                            ->action(fn (NutritionalSupplement $record) => $record->toggleStatus())

                    ),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                SelectFilter::make('status')
                    ->options(ModelStatus::class),

            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNutritionalSupplements::route('/'),
            'create' => Pages\CreateNutritionalSupplement::route('/create'),
            'edit' => Pages\EditNutritionalSupplement::route('/{record}/edit'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }


    public static function getNavigationGroup(): ?string
    {
        return __('menu.products');
    }

    public static function getPermissionPrefixes(): array
    {
        return [
            'view_any',
            'create',
            'update',
            'delete',
            'delete_any',
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
