<?php

namespace Tasawk\Filament\Resources\NutritionalSupplementResource\Pages;

use Tasawk\Filament\Resources\NutritionalSupplementResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListNutritionalSupplements extends ListRecords
{
    protected static string $resource = NutritionalSupplementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
