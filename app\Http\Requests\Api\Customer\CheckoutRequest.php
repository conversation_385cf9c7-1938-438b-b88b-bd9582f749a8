<?php

namespace Tasawk\Http\Requests\Api\Customer;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Tasawk\Enum\Catalog\OptionTypes;
use Tasawk\Models\Subscription\Question;
use Tasawk\Rules\IsValidCoupon;

class CheckoutRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'plan_id' => ['required', 'exists:plans,id'],
            'plan_price_id' => ['required', 'exists:plans_pricings,id'],
            'type' => ['required', Rule::in(['normal', 'scheduled'])],
            'start_date' => ['required_if:type,scheduled', 'date_format:Y-m-d', 'after:today'  ,  'before_or_equal:' .now()->addDays(30)->format('Y-m-d'),
        ],
            'age' => ['required'],
            'gender' => ['required', 'in:0,1'],
            'weight' => ['required'],
            'length' => ['required'],
            'body_images' => ['required_if:inbody_type,body_images', 'array'],
            'coupon_code' => ['nullable', 'exists:coupons,code', new IsValidCoupon],
            'payment_method' => ['required', 'in:myFatoorah'],
            'questions' => ['required_if:type,normal', 'array'],
            'questions.*.question_id' => ['required_with:questions', 'integer', 'exists:questions,id'],
            'questions.*.value' => [
                'required_with:questions',
                function ($attribute, $value, $fail) {
                    $questionId = $this->input(str_replace('.value', '.question_id', $attribute));
                    $question = Question::find($questionId);
                    if (! $question) {
                        $fail(__('validation.exists', ['attribute' => __('validation.attributes.question_id')]));

                        return;
                    }
                    switch ($question->type) {
                        case OptionTypes::TEXTAREA:
                            if (empty($value)) {
                                $fail(__('validation.required', ['attribute' => __('validation.attributes.value')]));
                            }
                            break;
                        case OptionTypes::RADIO:
                            if (! in_array($value, $question->values->pluck('id')->toArray())) {
                                $fail(__('validation.in', ['attribute' => __('validation.attributes.value')]));
                            }
                            break;
                        case OptionTypes::CHECKBOX:
                            $values = explode(',', $value);
                            $validValues = $question->values->pluck('id')->toArray();
                            foreach ($values as $v) {
                                if (! in_array($v, $validValues)) {
                                    $fail(__('validation.in', ['attribute' => __('validation.attributes.value')]));
                                }
                            }
                            break;
                        case OptionTypes::TIME:
                            if (! preg_match('/^([01]\d|2[0-3]):([0-5]\d)$/', $value)) {
                                $fail(__('validation.regex', ['attribute' => __('validation.attributes.value')]));
                            }
                            break;
                        case OptionTypes::DATE:
                            if (! preg_match('/^\d{4}-\d{2}-\d{2}$/', $value)) {
                                $fail(__('validation.regex', ['attribute' => __('validation.attributes.value')]));
                            }
                            break;
                        case OptionTypes::DATETIME:
                            if (! preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $value)) {
                                $fail(__('validation.regex', ['attribute' => __('validation.attributes.value')]));
                            }
                            break;
                        default:
                            break;
                    }
                },
            ],
            'inbody_type' => ['required_if:type,normal', Rule::in(['traditional_way', 'inbody_check','body_images'])],
            'images' => ['required_if:inbody_type,inbody_check', 'array'],
            'inbody' => ['required_if:inbody_type,traditional_way', 'array'],
            'inbody.chest' => ['required_if:inbody_type,traditional_way'],
            'inbody.right_arm' => ['required_if:inbody_type,traditional_way'],
            'inbody.left_arm' => ['required_if:inbody_type,traditional_way'],
            'inbody.stomach' => ['required_if:inbody_type,traditional_way'],
            'inbody.waist' => ['required_if:inbody_type,traditional_way'],
            'inbody.right_thigh' => ['required_if:inbody_type,traditional_way'],
            'inbody.left_thigh' => ['required_if:inbody_type,traditional_way'],
            'inbody.right_leg' => ['required_if:inbody_type,traditional_way'],
            'inbody.left_leg' => ['required_if:inbody_type,traditional_way'],
            'is_approved_conditions_subscription' => ['required_if:type,normal', 'in:1'],

        ];
    }

    public function messages()
    {
        $messages = [
            'start_date.required_if' => __('validation.required', ['attribute' => __('validation.attributes.start_date')]),
            'inbody.chest.required_if' => __('validation.required', ['attribute' => __('validation.attributes.chest')]),
            'inbody.right_arm.required_if' => __('validation.required', ['attribute' => __('validation.attributes.right_arm')]),
            'inbody.left_arm.required_if' => __('validation.required', ['attribute' => __('validation.attributes.left_arm')]),
            'images.required_if' => __('validation.required', ['attribute' => __('validation.attributes.images')]),
            'inbody.stomach.required_if' => __('validation.required', ['attribute' => __('validation.attributes.stomach')]),
            'inbody.waist.required_if' => __('validation.required', ['attribute' => __('validation.attributes.waist')]),
            'inbody.right_thigh.required_if' => __('validation.required', ['attribute' => __('validation.attributes.right_thigh')]),
            'inbody.left_thigh.required_if' => __('validation.required', ['attribute' => __('validation.attributes.left_thigh')]),
            'inbody.right_leg.required_if' => __('validation.required', ['attribute' => __('validation.attributes.right_leg')]),
            'inbody.left_leg.required_if' => __('validation.required', ['attribute' => __('validation.attributes.left_leg')]),
            // 'questions.required' => __('validation.required', ['attribute' => __('validation.attributes.question_id')]),
            'questions.question_id.required_with' => __('validation.required', ['attribute' => __('validation.attributes.question_id')]),
            'questions.question_id.exists' => __('validation.exists', ['attribute' => __('validation.attributes.question_id')]),
            'inbody.required_if' => __('validation.required', ['attribute' => __('validation.attributes.inbody')]),
            'start_date.after' => __('validation.after', ['attribute' => __('validation.attributes.start_date'), 'date' => __('validation.attributes.today')]),
            'questions.required_if' =>   __('validation.required', ['attribute' => __('validation.attributes.questions')]),
            'inbody_type.required_if' => __('validation.required', ['attribute' => __('validation.attributes.inbody_type')]),
            'start_date.before_or_equal' => __('validation.before_or_equal', ['attribute' => __('validation.attributes.start_date'), 'date' => __('validation.attributes.30_days_from_now')]),
            'is_approved_conditions_subscription.required_if' => __('validation.required', ['attribute' => __('validation.attributes.is_approved_conditions_subscription')]),
            'body_images.required_if' => __('validation.required', ['attribute' => __('validation.attributes.body_images')]),

        ];

        $questionIds = collect($this->input('questions', []))->pluck('question_id')->toArray();
        $options = ! empty($questionIds) ? Question::whereIn('id', $questionIds)->get() : collect();

        foreach ($this->input('questions', []) as $index => $question) {
            $option = $options->where('id', $question['question_id'])->first();
            $messages["questions.$index.value.required_with"] = __('validation.required', ['attribute' => $option?->name ?? __('validation.attributes.value')]);
        }

        return $messages;
    }
}
