<?php

namespace Tasawk\Filament\Resources\Meal\MealResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Wizard\Step;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Str;
use Tasawk\Enum\OrderStatus;
use Tasawk\Models\Catalog\Branch\Inventory;
use Tasawk\Models\Catalog\Option;
use Tasawk\Models\Catalog\Product;
use Tasawk\Models\Catalog\Size;
use Tasawk\Models\Catalog\ProductOption;
use Tasawk\Models\Catalog\Value;
use Tasawk\Models\Order;

class MealProductRelationManager extends RelationManager {

    protected static string $relationship = 'mealProducts';
    protected bool $allowsDuplicates = false;

    public function form(Form $form): Form {
        return $form
            ->schema([])
            ->columns(1);
    }

    public function table(Table $table): Table {
        return $table
            ->heading(__('sections.mealProducts'))
            ->columns([
                Tables\Columns\TextColumn::make('id'),
                Tables\Columns\TextColumn::make('option.name'),
                Tables\Columns\TextColumn::make('value'),

            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->form($this->wizardSchema()),

            ])
            ->actions([
                Tables\Actions\EditAction::make()->form($this->wizardSchema()),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public function wizardSchema(): array {
        return [
                Forms\Components\Select::make("product_id")
                ->label(__('forms.fields.product_title'))
                ->live()
                ->required()
                ->options(Product::get()->pluck('title', 'id'))
                ->searchable(),
            Forms\Components\Select::make("size_id")
                ->label(__('forms.fields.option_name'))
                ->live()
                
                ->required()
                ->options(Option::get()->pluck('name', 'id'))
                ->searchable(),
                Forms\Components\TextInput::make("value")
                    ->formatStateUsing(fn(Model $record,Get $get): string => $record->user->phone ?? $record->phone)
                    ->required(),

           
        
        ];
    }

    /**
     * @return string|null
     */
    public static function getModelLabel(): ?string {
     return __('sections.mealProducts');
    }

    
}