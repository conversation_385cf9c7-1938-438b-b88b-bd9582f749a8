<?php

namespace Tasawk\Http\Requests\Api\Customer\Profile;


use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Tasawk\Rules\KSAPhoneRule;
use Tasawk\Rules\PhoneNumber;

class UpdateCustomerProfileRequest extends FormRequest {

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize() {
        return true;
    }

    protected function prepareForValidation() {
        if($this->get('gender_enum') == null)
        {
            $gender = auth()->user()->gender;
        }else{
            $gender = $this->get('gender_enum') == 0 ? 'male' : 'female';
        }
        $this->merge([
            'name' => $this->get('full_name'),
            'gender' =>  $gender,
        ]);

    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules() {
        return [
            'avatar' => ['nullable','image'],
            'full_name' => ['required', 'string', 'max:150'],
            'phone' => [
                'required',
                Rule::unique('users')->ignore(auth()->id()),
                // 'exists:users',
                'regex:/^\d{8,15}$/',
                new PhoneNumber($this->input('country_code')),
            ],
            'country_code' => ['required', 'string', 'regex:/^\d{1,4}$/'],
            'email' => ['required', 'email', Rule::unique('users')->ignore(auth()->id())],
            'zone_id' => ['required','exists:zones,id'],
            'city_id' => ['required' ,'exists:cities,id'],
            'gender_enum' => ['required' ,'in:0,1'],
        ];
    }
}