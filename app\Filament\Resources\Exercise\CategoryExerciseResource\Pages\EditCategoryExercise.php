<?php

namespace Tasawk\Filament\Resources\Exercise\CategoryExerciseResource\Pages;

use Tasawk\Filament\Resources\Exercise\CategoryExerciseResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCategoryExercise extends EditRecord
{
    use EditRecord\Concerns\Translatable;

    protected static string $resource = CategoryExerciseResource::class;

    protected function getHeaderActions(): array {
        return [
            Actions\DeleteAction::make(),
            Actions\LocaleSwitcher::make(),

        ];
    }

    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }
}
