<?php

namespace Tasawk\Filament\Resources\Reports\RefundsReportResource\Pages;

use Tasawk\Filament\Resources\Reports\RefundsReportResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListRefundsReports extends ListRecords
{
    protected static string $resource = RefundsReportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}