<?php

namespace Tasawk\Http\Resources\Api\Chat;

use Illuminate\Http\Resources\Json\JsonResource;
use function Tasawk\Http\Resources\Api\Customer\User\setting_user;
use function Tasawk\Http\Resources\Api\Customer\User\upload_storage_url;

class UserResources extends JsonResource {

    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request) {

        return [
            'id' => $this->id,
            'name' => $this->name,
            'avatar' => $this->getFirstMediaUrl(),
        ];
    }
}
