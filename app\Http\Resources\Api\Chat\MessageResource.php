<?php

namespace Tasawk\Http\Resources\Api\Chat;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class MessageResource extends JsonResource {


    public function toArray($request) {
        // dd($this->transaction);
        return [
            'id' => $this->id,
            'from' => UserResources::make($this->from),
            'to' => UserResources::make($this->to),
            $this->mergeWhen($this->message != null, [
                'message' => $this->message,
            ]),
            $this->mergeWhen(($this->message == null || $this->type == 'image' || $this->type == 'text_image'), [
                'image' => $this->getFirstMediaUrl(),
            ]),
            $this->mergeWhen($this->type == 'url', [
                'total' => $this?->transaction?->price?->format('F'),
                'payment_status' => $this?->transaction?->status,
                'text' => __('forms.fields.invoice_text'). ' ' . $this?->transaction?->price?->format('F'),
            ]),
            'type' => $this?->type,
            'sender'=>$this->from_user_id ==auth()->id() ?'customer':'admin',
            'date' => $this->created_at->translatedFormat('Y-m-d H:i:s'),
        ];
    }
}
