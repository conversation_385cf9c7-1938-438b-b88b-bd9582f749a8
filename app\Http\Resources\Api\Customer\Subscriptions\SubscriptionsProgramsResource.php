<?php

namespace Tasawk\Http\Resources\Api\Customer\Subscriptions;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Tasawk\Settings\GeneralSettings;

class SubscriptionsProgramsResource extends JsonResource
{
    protected $index; // Add a property to store the index

    // Update the constructor to accept index
    public function __construct($resource, $index = null)
    {
        parent::__construct($resource);
        $this->index = $index + 1;
    }

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $setting = new GeneralSettings;
        return [
            // 'index' => $this->index,
            'sub_id' => $this->id,
            'subscription_id' => $this->subscription_id,
            'subscription_number' => $this?->subscription?->subscription_number,
            'program_id' => $this->program_id,
            'program_name' => $this->program->name,
            'status' => $this->status->getCustomrLabel(),
            'status_enum' => $this->status->value,
            'start_date' => $this?->start_date->format('Y-m-d'),
            'end_date' => $this?->end_date->format('Y-m-d'),
            'duration' => $this?->duration,
            'last_updated_at' => $this->updated_at->format('Y-m-d h:i a'),
            'created_at' => $this->created_at->format('Y-m-d h:i a'),
        ];
    }
}