<?php

namespace Tasawk\Http\Resources\Api\Customer\Subscriptions;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MealProductsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this?->id,
            'product_id' => $this?->product_id,
            'image' => $this?->product->getFirstMediaUrl(),
            'title' => $this?->product?->title,
            'description' => $this?->product?->description ?? '',
            'value' => $this?->value,

        ];
    }
}