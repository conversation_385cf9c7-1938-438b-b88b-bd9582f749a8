<?php

namespace Tasawk\Filament\Resources\Exercise\CategoryExerciseResource\Pages;

use Tasawk\Filament\Resources\Exercise\CategoryExerciseResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateCategoryExercise extends CreateRecord
{

    use CreateRecord\Concerns\Translatable;
    protected static string $resource = CategoryExerciseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }
}
