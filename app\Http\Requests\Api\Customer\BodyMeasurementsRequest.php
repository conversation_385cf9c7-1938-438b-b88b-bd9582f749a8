<?php

namespace Tasawk\Http\Requests\Api\Customer;

use Illuminate\Foundation\Http\FormRequest;

class BodyMeasurementsRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'inbody' => ['required', 'array'],
            'inbody.chest' => ['required'],
            'inbody.right_arm' => ['required'],
            'inbody.left_arm' => ['required'],
            'inbody.stomach' => ['required'],
            'inbody.waist' => ['required'],
            'inbody.right_thigh' => ['required'],
            'inbody.left_thigh' => ['required'],
            'inbody.right_leg' => ['required'],
            'inbody.left_leg' => ['required'],

        ];
    }

    public function Messages()
    {
        return [
            'inbody.chest.required' => __('validation.required', ['attribute' => __('validation.attributes.chest')]),
            'inbody.right_arm.required' => __('validation.required', ['attribute' => __('validation.attributes.right_arm')]),
            'inbody.left_arm.required' => __('validation.required', ['attribute' => __('validation.attributes.left_arm')]),
            'inbody.stomach.required' => __('validation.required', ['attribute' => __('validation.attributes.stomach')]),
            'inbody.waist.required' => __('validation.required', ['attribute' => __('validation.attributes.waist')]),
            'inbody.right_thigh.required' => __('validation.required', ['attribute' => __('validation.attributes.right_thigh')]),
            'inbody.left_thigh.required' => __('validation.required', ['attribute' => __('validation.attributes.left_thigh')]),
            'inbody.right_leg.required' => __('validation.required', ['attribute' => __('validation.attributes.right_leg')]),
            'inbody.left_leg.required' => __('validation.required', ['attribute' => __('validation.attributes.left_leg')]),
        ];
    }
}