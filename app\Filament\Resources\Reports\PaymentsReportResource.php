<?php

namespace Tasawk\Filament\Resources\Reports;

use <PERSON>zhan<PERSON>alleh\FilamentShield\Contracts\HasShieldPermissions;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use Tasawk\Filament\Resources\Reports\PaymentsReportResource\Pages;
use Tasawk\Models\Reports\PaymentsReport;
use Tasawk\Traits\Filament\HasTranslationLabel;

class PaymentsReportResource extends Resource implements HasShieldPermissions
{
    use HasTranslationLabel, Translatable;

    protected static ?string $model = PaymentsReport::class;

    protected static ?int $navigationSort = 1;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->sortable()->label(__('forms.fields.index')),
                TextColumn::make('subscription_number')->copyable()
                    ->searchable(query: fn ($query, $search) => $query->where('id', '=', substr($search, 1))),
                TextColumn::make('payment_data.paid_at')
                    ->label(__('forms.fields.paid_at'))
                    ->formatStateUsing(function (PaymentsReport $record) {
                        return (new \DateTime($record->payment_data['paid_at']))->format('Y-m-d H:i:s');
                    }),
                TextColumn::make('payment_data.invoiceId')
                    ->label(__('forms.fields.invoice_id'))
                    ->formatStateUsing(function (PaymentsReport $record) {
                        return $record->payment_data['invoiceId'];
                    }),
                TextColumn::make('plan.name')->searchable(),
                TextColumn::make('customer.name')->searchable(),
                TextColumn::make('customer.phone')->searchable()->label(__('forms.fields.phone')),
                TextColumn::make('payment_data.method')->copyable()
                    ->searchable(query: fn ($query, $search) => $query->where('payment_data->method', $search))
                    ->label(__('forms.fields.payment_method')),
                IconColumn::make('payment_data.invoiceURL')->color('success')->icon('heroicon-o-document-currency-pound')->label(__('forms.fields.incoice_url'))
                    ->url(fn (Model $record) => $record->payment_data['invoiceURL'], '__blank'),
                TextColumn::make('total')
                    ->searchable()
                    ->summarize(Tables\Columns\Summarizers\Sum::make()->money('SAR'))
                    ->label(__('forms.fields.total_amount')),
            ])
            ->filters([
                //
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    ExportBulkAction::make()->exports([
                        ExcelExport::make('CSV')
                            ->fromTable()
                            ->withFilename(fn () => static::getPluralLabel().'-'.now()->format('Y-m-d'))
                            ->withWriterType(\Maatwebsite\Excel\Excel::XLSX),
                    ]),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPaymentsReports::route('/'),
            // 'create' => Pages\CreatePaymentsReport::route('/create'),
            // 'edit' => Pages\EditPaymentsReport::route('/{record}/edit'),
        ];
    }

    public static function getNavigationGroup(): ?string
    {
        return __('menu.reports');
    }

    public static function getPermissionPrefixes(): array
    {
        return [
            'view_any',
            'export',
        ];
    }
}
