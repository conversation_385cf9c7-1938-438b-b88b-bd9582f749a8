<?php

namespace Tasawk\Filament\Resources\NutritionalSupplementResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Resources\Pages\EditRecord\Concerns\Translatable;
use Tasawk\Filament\Resources\NutritionalSupplementResource;

class EditNutritionalSupplement extends EditRecord
{
    use Translatable;

    protected static string $resource = NutritionalSupplementResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }


    protected function afterSave(): void
    {
        $product = $this->record;
        $data = $this->data['options'];
        foreach ($data as $key => $value) {
            $options[$key] = ['value' => $value];
        }
        $product->options()->sync($options);
    }
}