<?php

namespace Tasawk\Filament\Resources\Meal\CategoryMealResource\Pages;

use Tasawk\Filament\Resources\Meal\CategoryMealResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditCategoryMeal extends EditRecord
{
    use EditRecord\Concerns\Translatable;

    protected static string $resource = CategoryMealResource::class;

    protected function getHeaderActions(): array {
        return [
            Actions\DeleteAction::make(),
            Actions\LocaleSwitcher::make(),

        ];
    }

    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }
}
