<?php

namespace Tasawk\Filament\Resources\Meal\CategoryMealResource\Pages;

use Tasawk\Filament\Resources\Meal\CategoryMealResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateCategoryMeal extends CreateRecord
{

    use CreateRecord\Concerns\Translatable;
    protected static string $resource = CategoryMealResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }
}
