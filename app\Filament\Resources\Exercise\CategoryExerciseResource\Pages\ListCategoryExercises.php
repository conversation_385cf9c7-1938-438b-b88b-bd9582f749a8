<?php

namespace Tasawk\Filament\Resources\Exercise\CategoryExerciseResource\Pages;

use Tasawk\Filament\Resources\Exercise\CategoryExerciseResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListCategoryExercises extends ListRecords
{
    protected static string $resource = CategoryExerciseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
