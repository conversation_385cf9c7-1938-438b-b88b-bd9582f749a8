<?php

namespace Tasawk\Http\Resources\Api\Customer;

use Illuminate\Http\Resources\Json\JsonResource;

class ProfileHistoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'main_information' => [
                'age' => $this->age,
                'gender_enum' => $this->gender == 'male' ? 0 : 1,
                'gender' => __('forms.fields.'.$this->gender),
                'weight' => $this->wight,
                'length' => $this->length,
            ],
            'sports_health_information' => QuestionResource::collection($this->questions),
            'body_measurements' => [
                'chest_circumference' =>   $this->chest_circumference  .'cm',
            'lower_chest' =>  $this->lower_chest .'cm',
            'waist' =>   $this->waist .'cm',
            'abdomen' =>   $this->abdomen .'cm',
            'buttocks' =>  $this->buttocks .'cm',
            'upper_arm' =>   $this->upper_arm .'cm',
            'thigh' =>   $this->thigh .'cm',
            'calf' =>   $this->calf .'cm',
            ],
            'body_images' => $this->body_images_format,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),

        ];
    }
}