<?php

namespace Tasawk\Filament\Resources\Catalog;

use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Tasawk\Enum\ModelStatus;
use Tasawk\Filament\Resources\Catalog\ProductResource\Pages;
use Tasawk\Filament\Resources\Catalog\ProductResource\RelationManagers\OptionRelationManager;
use Tasawk\Models\Catalog\Option;
use Tasawk\Models\Catalog\Product;
use Tasawk\Models\Catalog\ProductCategory;
use Tasawk\Models\Catalog\Unit;
use Tasawk\Traits\Filament\HasTranslationLabel;

class ProductResource extends Resource implements HasShieldPermissions
{
    use HasTranslationLabel;
    use Translatable;

    protected static ?int $navigationSort = 4;

    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-pencil-square';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()->schema([
                    Forms\Components\Section::make('general')->schema([
                        TextInput::make('title')
                            ->required()
                            ->translateLabel(),
                        Textarea::make('description')
                            ->required()
                            ->translateLabel(),
                        SpatieMediaLibraryFileUpload::make('image')
                            ->required(),
                        Select::make('category_id')
                            ->required()
                            ->options(ProductCategory::enabled()->pluck('name', 'id'))
                            ->getOptionLabelFromRecordUsing(fn (ProductCategory $record) => "{$record->getTranslation('name', 'en')} - {$record->getTranslation('name', 'ar')}")
                            ->searchable(['name->ar', 'name->en']),
                        Select::make('unit_id')
                            ->required()
                            ->live()
                            ->label(__('forms.fields.unit_measurement'))
                            ->options(Unit::enabled()->pluck('name', 'id'))
                            ->getOptionLabelFromRecordUsing(fn (Unit $record) => "{$record->getTranslation('name', 'en')} - {$record->getTranslation('name', 'ar')}")
                            ->searchable(['name->ar', 'name->en']),
                        Toggle::make('status')->default(1)
                            ->label(__('forms.fields.enabled'))
                            ->onColor('success')
                            ->offColor('danger')
                            ->translateLabel(),
                    ]),
                ])->columnSpan(3),
                Forms\Components\Group::make()->schema([
                    Forms\Components\Section::make('options')
                        ->schema(static::productOptions())
                        ->heading(__('forms.fields.options_heading'))
                        ->description(fn($get) => !$get('unit_id') ? '' : __('forms.fields.options_description:unit', ['unit' => Unit::find($get('unit_id'))?->name ?? ''])),
                ])->columnSpan(2),
            ])->columns(5);
    }

    public static function productOptions()
    {
        $schema = [];
        foreach (Option::latest()->get() as $option) {
            $schema[] = TextInput::make("options.{$option->id}")
                ->required()
                ->formatStateUsing(fn ($record) => optional($record?->options?->where('id', $option?->id)?->first()?->pivot)?->value ?? '')
                ->label($option->name)
                ->suffix($option?->unit?->name);
        }

        return $schema;
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('index')->rowIndex(),
                TextColumn::make('id'),
                SpatieMediaLibraryImageColumn::make('image')
    ->getStateUsing(fn (Product $record) => $record->getFirstMediaUrl('default') ?? ''),

                TextColumn::make('title')
                    ->searchable(),
                TextColumn::make('category.name')
                    ->searchable(),
                IconColumn::make('status')
                    ->boolean()
                    ->action(
                        Action::make('Active')
                            ->label(fn (Product $record): string => $record->status ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            ->disabled(fn (Model $record): bool => ! auth()->user()->can('update', $record))
                            ->requiresConfirmation()
                            ->action(fn (Product $record) => $record->toggleStatus())

                    ),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                SelectFilter::make('status')
                    ->options(ModelStatus::class),
                SelectFilter::make('category_id')
                    ->options(fn () => ProductCategory::enabled()->pluck('name', 'id')),

            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);

    }

    public static function getRelations(): array
    {
        return [
            // OptionRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
        ];
    }

    public static function getNavigationGroup(): ?string
    {
        return __('menu.products');
    }

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }

    public static function getPermissionPrefixes(): array
    {
        return [
            'view_any',
            'create',
            'update',
            'delete',
            'delete_any',
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}