<?php

namespace Tasawk\Filament\Resources\Subscription\QuestionResource\Schema;

use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Tasawk\Enum\Catalog\OptionTypes;
use Tasawk\Models\Subscription\Question;
use Tasawk\Models\Subscription\Value;

trait QuestionSchema
{
    public function schema(): array
    {
        return [
            Grid::make()->schema([
                Section::make('General')->schema([

                    TextInput::make('name')
                        ->required()
                        ->translateLabel(),

                    Select::make('type')
                        ->options(OptionTypes::class)
                        ->required()
                        ->live(),
                    Toggle::make('status')
                        ->default(1)
                        ->onColor('success')
                        ->offColor('danger'),
                        Toggle::make('is_dependency')
                        ->default(0)
                        ->live()
                        ->onColor('success')
                        ->offColor('danger'),
                    Select::make('parent_question_id')
                        ->label(__('forms.fields.parent_question_name'))
                        ->live()
                        ->visible(fn ($get) => $get('is_dependency') == 1)
                        ->options(fn () => Question::all()->pluck('name', 'id')),

                        Select::make('value_dependency')
                        ->label(__('forms.fields.value_dependency_name'))
                        ->options(fn ($get) => Value::where('question_id', $get('parent_question_id'))->pluck('value', 'id'))
                        ->hidden(fn ($get) => !$get('parent_question_id') || $get('is_dependency') == 0),

                ]),

                Section::make('Values')->schema([
                    Repeater::make('values')
                        ->schema($this->valuesSchema())
                        ->label('')
                        ->relationship('values')
                        ->cloneable()
                        ->reorderable()
                        ->orderColumn()
                        ->addActionLabel(__('forms.actions.add')),
                ])->hidden(fn ($get) => ! $get('type') || ($get('type') == OptionTypes::TEXTAREA->value || $get('type') == OptionTypes::DATE->value || $get('type') == OptionTypes::DATETIME->value || $get('type') == OptionTypes::TIME->value)),

            ])->columns(1),
        ];
    }

    // public function valuesSchema(): array
    // {
    //     $schema = [];
    //     foreach ((new self)->getTranslatableLocales() as $locale) {
    //         $schema[] = TextInput::make('value.'.$locale)
    //             ->label(__('forms.fields.value_name'))
    //             ->required()
    //             ->live()
    //             ->visible(fn ($get) => $locale == $this->getActiveActionsLocale())
    //             ->afterStateHydrated(fn (TextInput $component) => $component->state($component->getModelInstance()->getTranslation('value', $locale)));
    //         $schema[] = Hidden::make('value.'.$locale)->default(fn ($get) => $get('value.'.$locale));
    //     }

    //     return $schema;
    // }

    public function valuesSchema(): array {
        $schema = [];
        foreach ((new self)->getTranslatableLocales() as $locale) {
            $schema[] = TextInput::make('value.' . $locale)
                ->label(__('forms.fields.value_name'))
                ->required()
                ->live()
                ->visible(fn ($get) => $locale == $this->getActiveActionsLocale())
                ->afterStateHydrated(fn (TextInput $component) => $component->state($component->getModelInstance()->getTranslation('value', $locale)));
            $schema[] = Hidden::make('value.'.$locale)->default(fn ($get) => $get('value.'.$locale));
        }

        return $schema;
    }
}