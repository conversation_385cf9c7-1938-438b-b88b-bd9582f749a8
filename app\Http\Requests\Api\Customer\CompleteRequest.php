<?php

namespace Tasawk\Http\Requests\Api\Customer;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Tasawk\Enum\Catalog\OptionTypes;
use Tasawk\Models\Subscription\Question;
use Tasawk\Rules\IsValidCoupon;

class CompleteRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [

'body_images' => ['required_if:inbody_type,body_images', 'array'],
            'questions' => ['required', 'array'],
            'questions.*.question_id' => ['required_with:questions', 'integer', 'exists:questions,id'],
            'questions.*.value' => [
                'required_with:questions',
                function ($attribute, $value, $fail) {
                    $questionId = $this->input(str_replace('.value', '.question_id', $attribute));
                    $question = Question::find($questionId);
                    if (! $question) {
                        $fail(__('validation.exists', ['attribute' => __('validation.attributes.question_id')]));

                        return;
                    }
                    switch ($question->type) {
                        case OptionTypes::TEXTAREA:
                            if (empty($value)) {
                                $fail(__('validation.required', ['attribute' => __('validation.attributes.value')]));
                            }
                            break;
                        case OptionTypes::RADIO:
                            if (! in_array($value, $question->values->pluck('id')->toArray())) {
                                $fail(__('validation.in', ['attribute' => __('validation.attributes.value')]));
                            }
                            break;
                        case OptionTypes::CHECKBOX:
                            $values = explode(',', $value);
                            $validValues = $question->values->pluck('id')->toArray();
                            foreach ($values as $v) {
                                if (! in_array($v, $validValues)) {
                                    $fail(__('validation.in', ['attribute' => __('validation.attributes.value')]));
                                }
                            }
                            break;
                        case OptionTypes::TIME:
                            if (! preg_match('/^([01]\d|2[0-3]):([0-5]\d)$/', $value)) {
                                $fail(__('validation.regex', ['attribute' => __('validation.attributes.value')]));
                            }
                            break;
                        case OptionTypes::DATE:
                            if (! preg_match('/^\d{4}-\d{2}-\d{2}$/', $value)) {
                                $fail(__('validation.regex', ['attribute' => __('validation.attributes.value')]));
                            }
                            break;
                        case OptionTypes::DATETIME:
                            if (! preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $value)) {
                                $fail(__('validation.regex', ['attribute' => __('validation.attributes.value')]));
                            }
                            break;
                        default:
                            break;
                    }
                },
            ],
            'inbody_type' => ['required', Rule::in(['traditional_way', 'inbody_check','body_images'])],
            'images' => ['required_if:inbody_type,inbody_check', 'array'],
            'inbody' => ['required_if:inbody_type,traditional_way', 'array'],
            'inbody.chest_circumference' => ['required_if:inbody_type,traditional_way'],
            'inbody.lower_chest' => ['required_if:inbody_type,traditional_way'],
            'inbody.waist' => ['required_if:inbody_type,traditional_way'],
            'inbody.abdomen' => ['required_if:inbody_type,traditional_way'],
            'inbody.buttocks' => ['required_if:inbody_type,traditional_way'],
            'inbody.upper_arm' => ['required_if:inbody_type,traditional_way'],
            'inbody.calf' => ['required_if:inbody_type,traditional_way'],
            'inbody.thigh' => ['required_if:inbody_type,traditional_way'],
            'is_approved_conditions_subscription' => ['required', 'in:1'],

        ];
    }

    public function messages()
    {
        $messages = [
            'start_date.required_if' => __('validation.required', ['attribute' => __('validation.attributes.start_date')]),
            'inbody.chest_circumference.required_if' => __('validation.required', ['attribute' => __('validation.attributes.chest_circumference')]),
            'inbody.lower_chest.required_if' => __('validation.required', ['attribute' => __('validation.attributes.lower_chest')]),
            'inbody.waist.required_if' => __('validation.required', ['attribute' => __('validation.attributes.waist')]),
            'images.required_if' => __('validation.required', ['attribute' => __('validation.attributes.images')]),
            'inbody.abdomen.required_if' => __('validation.required', ['attribute' => __('validation.attributes.abdomen')]),
            'inbody.buttocks.required_if' => __('validation.required', ['attribute' => __('validation.attributes.buttocks')]),
            'inbody.upper_arm.required_if' => __('validation.required', ['attribute' => __('validation.attributes.upper_arm')]),
            'inbody.calf.required_if' => __('validation.required', ['attribute' => __('validation.attributes.calf')]),
            'inbody.thigh.required_if' => __('validation.required', ['attribute' => __('validation.attributes.thigh')]),
            'questions.required' => __('validation.required', ['attribute' => __('validation.attributes.question_id')]),
            'questions.question_id.required_with' => __('validation.required', ['attribute' => __('validation.attributes.question_id')]),
            'questions.question_id.exists' => __('validation.exists', ['attribute' => __('validation.attributes.question_id')]),
            'inbody.required_if' => __('validation.required', ['attribute' => __('validation.attributes.inbody')]),
            'start_date.after' => __('validation.after', ['attribute' => __('validation.attributes.start_date'), 'date' => __('validation.attributes.today')]),
            'questions.required_if' =>   __('validation.required', ['attribute' => __('validation.attributes.questions')]),
            'inbody_type.required_if' => __('validation.required', ['attribute' => __('validation.attributes.inbody_type')]),
            'body_images.required_if' => __('validation.required', ['attribute' => __('validation.attributes.body_images')]),
        ];

        $questionIds = collect($this->input('questions', []))->pluck('question_id')->toArray();
        $options = ! empty($questionIds) ? Question::whereIn('id', $questionIds)->get() : collect();

        foreach ($this->input('questions', []) as $index => $question) {
            $option = $options->where('id', $question['question_id'])->first();
            $messages["questions.$index.value.required_with"] = __('validation.required', ['attribute' => $option?->name ?? __('validation.attributes.value')]);
        }

        return $messages;
    }
}