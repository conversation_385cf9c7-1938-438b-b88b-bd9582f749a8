<?php

namespace Tasawk\Filament\Resources\Reports\IssuingInvoiceReportResource\Pages;

use Tasawk\Filament\Resources\Reports\IssuingInvoiceReportResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListIssuingInvoiceReports extends ListRecords
{
    protected static string $resource = IssuingInvoiceReportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make(),
        ];
    }
}