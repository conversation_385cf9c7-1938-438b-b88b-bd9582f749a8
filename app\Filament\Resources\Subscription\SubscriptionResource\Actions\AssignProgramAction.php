<?php

namespace Tasawk\Filament\Resources\Subscription\SubscriptionResource\Actions;

use Carbon\Carbon;
use Filament\Actions\Action;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Get;
use Filament\Notifications\Notification as FilamentNotification;
use Illuminate\Support\Facades\DB;
use Tasawk\Enum\SubscriptionStatus;
use Tasawk\Models\Catalog\Option;
use Tasawk\Models\Exercise\Exercise;
use Tasawk\Models\Meal\Meal;
use Tasawk\Models\NutritionalSupplement;
use Tasawk\Models\Program\MealProgram;
use Tasawk\Models\Program\Program;
use Tasawk\Models\Subscription\Subscription;
use Tasawk\Models\Subscription\SubscriptionProgramExercise;
use Tasawk\Models\Subscription\SubscriptionProgramMeal;
use Tasawk\Models\Subscription\SubscriptionProgramMealProduct;
use Tasawk\Models\Subscription\SubscriptionProgramNutritionalSupplement;

class AssignProgramAction
{
    public static function make()
    {
        return Action::make('assign-program')
            ->modalWidth('7xl')
            ->label(__('forms.fields.assign-program'))
            ->icon('heroicon-o-plus-circle')
            ->color('success')
            ->form([
                Wizard::make(fn (Get $get) => [
                    Wizard\Step::make('basic_information')
                        ->label(__('sections.basic_information'))
                        ->schema([

                            Select::make('program_id')
                                ->options(Program::where('status', 1)->pluck('name', 'id'))
                                ->required()
                                ->label(__('forms.fields.program'))
                                ->reactive()
                                ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                    if ($state) {
                                        $program = Program::find($state);
                                        if ($program) {
                                            $set('duration', $program->duration);
                                            $set('name', $program->name);
                                            foreach (range(1, $get('duration')) as $day) {
                                                $program_exercises = $program->exercises()->where('sort', $day)->get();
                                                $program_meals = $program->meals()->where('sort', $day)->get();
                                                foreach ($program_exercises as $program_exercise) {
                                                    $exercisePath = "days.{$day}.exercises.{$program_exercise->id}";
                                                    $set("{$exercisePath}.exercise_name", $program_exercise->name);
                                                    $set("{$exercisePath}.exercise_id", $program_exercise->id);
                                                    $set("{$exercisePath}.number_of_session", $program_exercise->number_of_session);
                                                    $set("{$exercisePath}.number_of_repetition", $program_exercise->number_of_repetition);
                                                    $set("{$exercisePath}.rest", $program_exercise->rest);
                                                }
                                                foreach ($program_meals as $meal) {
                                                    $mealPath = "days.{$day}.meals.{$meal->meal_id}";

                                                    foreach ($meal->products as $product) {
                                                        //                                            dd($product->product);
                                                        $set("$mealPath.products.{$product->id}.product_name", $product->product->title);
                                                        $set("$mealPath.products.{$product->id}.product_id", $product->product->id);
                                                        $set("$mealPath.products.{$product->id}.value", $product->value);
                                                    }

                                                    $set("{$mealPath}.meal_id", $meal->meal_id);
                                                    $set("{$mealPath}.sort", $day);
                                                    $set("{$mealPath}.sum_of_protien", $meal->sum_of_protien);
                                                    $set("{$mealPath}.sum_of_fat", $meal->sum_of_fat);
                                                    $set("{$mealPath}.sum_of_calories", $meal->sum_of_calories);
                                                    $set("{$mealPath}.sum_of_carbohydrate", $meal->sum_of_carbohydrate);

                                                }
                                            }
                                        }
                                    }
                                }),

                            TextInput::make('duration')
                                ->default(1)
                                ->suffix(__('forms.suffixes.day'))
                                ->required(),
                            DatePicker::make('start_date')
                                ->required()
                                ->label(__('forms.fields.start_date')),
                        ]),
                    ...self::buildDaysWizardTabs($get('duration'), $get('program_id')),
                ])
                    ->startOnStep(1),
            ])
            ->afterFormValidated(fn () => true)
            ->afterFormFilled(fn () => true)
            ->afterFormFilled(fn () => true)
            ->action(function (array $data, Subscription $record): void {

                $subscription = Subscription::findOrFail($record->id);
                $subscription->program_id = $data['program_id'];
                $subscription->status = SubscriptionStatus::INREVIEW;
                $subscription->save();
                $subscription_program = $subscription->programs()->create([
                    'program_id' => $data['program_id'],
                    'status' => SubscriptionStatus::INREVIEW,
                    'start_date' =>  Carbon::parse($data['start_date']),
                    'end_date' =>  Carbon::parse($data['start_date'])->addDays($data['duration']),
                    'customer_id' => $subscription->customer_id,
                    'duration' => $data['duration'],
                ]);
                foreach ($data['days'] as $key => $day) {
                    // dd($day['nutritional_supplements']);
                    $program_exercises = $day['exercises'];
                    foreach ($program_exercises as $program_exercise) {
                        SubscriptionProgramExercise::create([
                            'subscription_program_id' => $subscription_program->id,
                            'exercise_id' => $program_exercise['exercise_id'],
                            'number_of_session' => $program_exercise['number_of_session'],
                            'number_of_repetition' => $program_exercise['number_of_repetition'],
                            'rest' => $program_exercise['rest'],
                            'sort' => $key,
                        ]);
                    }
                    $meals = $day['meals'];
                    foreach ($meals as $meal) {
                        $subscription_program_meal = SubscriptionProgramMeal::create([
                            'subscription_program_id' => $subscription_program->id,
                            'meal_id' => $meal['meal_id'],
                            'sort' => $key,
                            'sum_of_protien' => $meal['sum_of_protien'],
                            'sum_of_fat' => $meal['sum_of_fat'],
                            'sum_of_calories' => $meal['sum_of_calories'],
                            'sum_of_carbohydrate' => $meal['sum_of_carbohydrate'],
                        ]);
                        foreach ($meal['products'] as $product) {
                            SubscriptionProgramMealProduct::create([
                                'subscription_program_meal_id' => $subscription_program_meal->id,
                                'product_id' => $product['product_id'],
                                'value' => $product['value'],
                            ]);
                        }
                    }
                    $nutritional_supplements = $day['nutritional_supplements'];
                        foreach ($nutritional_supplements as $nutritional_supplement) {
                            SubscriptionProgramNutritionalSupplement::create([
                                'subscription_program_id' => $subscription_program->id,
                                'nutritional_supplement_id' => $nutritional_supplement['nutritional_supplement_id'],
                                'value' => $nutritional_supplement['value'],
                                'sum_of_protien' => $nutritional_supplement['nutritional_sum_of_protien'],
                                'sum_of_fat' => $nutritional_supplement['nutritional_sum_of_fat'],
                                'sum_of_calories' => $nutritional_supplement['nutritional_sum_of_calories'],
                                'sum_of_carbohydrate' => $nutritional_supplement['nutritional_sum_of_carbohydrate'],
                                'sort' => $key,
                            ]);

                        }

                }
                FilamentNotification::make()
                    ->success()
                    ->title(__('panel.messages.success'))
                    ->body(__('panel.messages.assign-program'))
                    ->persistent()
                    ->send();
            });
    }

    public static function buildDaysWizardTabs($days): array
    {
        $schema = [];
        if ($days <= 0) {
            return $schema;
        }
        foreach (range(1, $days) as $day) {

            $schema[] = Wizard\Step::make($day)
                ->icon('heroicon-m-shopping-bag')
                ->schema([
                    ...self::buildMealsRepeater($day),
                    ...self::buildExerciseRepeater($day),
                    ...self::buildNutritionalSupplementRepeater($day),

                ])->statePath('days.'.$day)
                ->columns(2);
        }

        return $schema;
    }

    public static function buildNutritionalSupplementRepeater($day): array
    {
        $schema = [
            Section::make('nutritional_supplements')
                ->label(__('menu.nutritional_supplements'))
                ->schema([
                    Repeater::make('nutritional_supplements')
                        ->label('')
                        ->schema([
                            Hidden::make('sort')->default($day),
                            Select::make('nutritional_supplement_id')
                                ->required()
                                ->options(NutritionalSupplement::enabled()->pluck('name', 'id'))
                                ->getOptionLabelFromRecordUsing(fn (NutritionalSupplement $record) => "{$record->getTranslation('name', 'en')} - {$record->getTranslation('name', 'ar')}")
                                ->searchable(['name->ar', 'name->en'])
                                ->reactive()
                                ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                    $NutritionalSupplement = NutritionalSupplement::find($state);
                                    if ($NutritionalSupplement) {
                                        $value = 1;
                                        self::computeNutritionalSupplementTotals($set, $NutritionalSupplement, $value);
                                    }
                                }),
                            TextInput::make('value')
                                ->default(1)
                                ->reactive()
                                ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                    $NutritionalSupplement = NutritionalSupplement::find($get('nutritional_supplement_id'));
                                    if ($NutritionalSupplement) {
                                        self::computeNutritionalSupplementTotals($set, $NutritionalSupplement, $state);
                                    }
                                })
                                ->label(__('forms.fields.quantity'))
                                ->required(),
                            Section::make('totals')
                                ->schema([
                                    TextInput::make('nutritional_sum_of_protien')
                                        ->label(__('forms.fields.sum_of_protien'))
                                        ->required()
                                        ->suffix(fn () => Option::where('id', '2')->first()?->unit?->name),
                                    // ->disabled(),
                                    TextInput::make('nutritional_sum_of_calories')
                                        ->label(__('forms.fields.sum_of_calories'))
                                        ->required()
                                        ->suffix(fn () => Option::where('id', '1')->first()?->unit?->name),
                                    // ->disabled(),
                                    TextInput::make('nutritional_sum_of_fat')
                                        ->suffix(fn () => Option::where('id', '4')->first()?->unit?->name)
                                        ->required()
                                        ->label(__('forms.fields.sum_of_fat')),
                                    // ->disabled(),
                                    TextInput::make('nutritional_sum_of_carbohydrate')
                                        ->suffix(fn () => Option::where('id', '3')->first()?->unit?->name)
                                        ->required()
                                        ->label(__('forms.fields.sum_of_carbohydrate')),
                                    // ->disabled(),
                                ])
                                ->reactive()
                                ->hidden(fn ($get) => $get('nutritional_supplement_id') === null),
                        ])
                        ->defaultItems(1)
                    ->minItems(1)
                    ->required(),
                ])->columnSpan(2),
        ];

        return $schema;

    }

    public static function buildMealsRepeater($day): array
    {
        return [
            Section::make('meals')
                ->label(__('menu.meals'))
                ->schema([
                    Repeater::make('meals')
                        ->label('')
                        ->schema([
                            Hidden::make('sort')->default($day),
                            Select::make('meal_id')
                                ->required()
                                ->reactive()
                                ->options(Meal::enabled()->pluck('name', 'id'))
                                ->searchable(['name->ar', 'name->en'])
                                ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                    $set('products', []);
                                    $meal = Meal::with('products')->find($state);
                                    if ($meal) {
                                        foreach ($meal->products as $product) {
                                            $set("products.{$product->id}.product_name", $product->product->title);
                                            $set("products.{$product->id}.product_id", $product->product->id);
                                            $set("products.{$product->id}.value", $product->value);
                                        }
                                        self::computeTotals($set, $meal);
                                    }
                                }),
                            Repeater::make('products')
                                ->addable(false)
                                ->schema([
                                    TextInput::make('product_name')
                                        ->formatStateUsing(fn ($record) => $record?->product?->title)
                                        ->label(__('forms.fields.product_title'))
                                        ->disabled(),
                                    Hidden::make('product_id')
                                        ->default(fn ($record) => $record->product_id)
                                        ->required(),

                                    TextInput::make('value')
                                        ->label(__('forms.fields.quantity'))
                                        // ->afterStateUpdated(fn ($state, callable $set) => self::computeTotals($set, $state))
                                        ->required(),
                                ])
                                ->defaultItems(0)
//                                ->relationship('products')
                                ->columns(2)
                                // ->afterStateUpdated(fn ($state, callable $set) => self::updateTotals($set, $state))
                                // ->afterStateHydrated(fn ($state, callable $set) => self::updateTotals($set, $state))
                                ->hidden(fn ($get) => $get('meal_id') === null),
                            Section::make('totals')
                                ->schema([
                                    TextInput::make('sum_of_protien')
                                        ->label(__('forms.fields.sum_of_protien'))
                                        ->suffix(fn () => Option::where('id', '2')->first()?->unit?->name),
                                    // ->disabled(),
                                    TextInput::make('sum_of_calories')
                                        ->label(__('forms.fields.sum_of_calories'))
                                        ->suffix(fn () => Option::where('id', '1')->first()?->unit?->name),
                                    // ->disabled(),
                                    TextInput::make('sum_of_fat')
                                        ->suffix(fn () => Option::where('id', '4')->first()?->unit?->name)
                                        ->label(__('forms.fields.sum_of_fat')),
                                    // ->disabled(),
                                    TextInput::make('sum_of_carbohydrate')
                                        ->suffix(fn () => Option::where('id', '3')->first()?->unit?->name)
                                        ->label(__('forms.fields.sum_of_carbohydrate')),
                                    // ->disabled(),
                                ])
                                ->reactive()
                                ->hidden(fn ($get) => $get('meal_id') === null),
                        ])
                        ->columns(1),

                ])
                ->columnSpan(1),
        ];
    }

    public static function computeTotals(callable $set, $meal)
    {
        $set('sum_of_protien', $meal->sum_of_protien);
        $set('sum_of_calories', $meal->sum_of_calories);
        $set('sum_of_fat', $meal->sum_of_fat);
        $set('sum_of_carbohydrate', $meal->sum_of_carbohydrate);
    }

    public static function computeNutritionalSupplementTotals(callable $set, $NutritionalSupplement, $value)
    {
        $sumOfProtien = (int) DB::table('nutritional_supplement_option')
            ->where('nutritional_supplement_id', $NutritionalSupplement->id)
            ->where('option_id', 2)
            ->sum(DB::raw('CAST(value AS SIGNED)')) * (int) $value;
        $sumOfCalories = (int) DB::table('nutritional_supplement_option')
            ->where('nutritional_supplement_id', $NutritionalSupplement->id)
            ->where('option_id', 1)
            ->sum(DB::raw('CAST(value AS SIGNED)')) * (int) $value;
        $sumOfFat = (int) DB::table('nutritional_supplement_option')
            ->where('nutritional_supplement_id', $NutritionalSupplement->id)
            ->where('option_id', 4)
            ->sum(DB::raw('CAST(value AS SIGNED)')) * (int) $value;
        $sumOfCarbohydrate = (int) DB::table('nutritional_supplement_option')
            ->where('nutritional_supplement_id', $NutritionalSupplement->id)
            ->where('option_id', 3)
            ->sum(DB::raw('CAST(value AS SIGNED)')) * (int) $value;

        $set('nutritional_sum_of_protien', $sumOfProtien);
        $set('nutritional_sum_of_calories', $sumOfCalories);
        $set('nutritional_sum_of_fat', $sumOfFat);
        $set('nutritional_sum_of_carbohydrate', $sumOfCarbohydrate);

    }

    public static function buildExerciseRepeater($day): array
    {
        $schema = [
            Section::make('exercises')
                ->label(__('menu.exercises'))
                ->schema([
                    Repeater::make('exercises')
                        ->label('')
                        ->schema([
                            Hidden::make('sort')->default($day),
                            Select::make('exercise_id')
                                ->required()
                                ->options(Exercise::enabled()->pluck('name', 'id'))
                                ->getOptionLabelFromRecordUsing(fn (Exercise $record) => "{$record->getTranslation('name', 'en')} - {$record->getTranslation('name', 'ar')}")
                                ->searchable(['name->ar', 'name->en'])
                                ->reactive()
                                ->afterStateUpdated(function ($state, callable $set) {
                                    $exercise = Exercise::find($state);
                                    if ($exercise) {
                                        $set('number_of_session', $exercise->number_of_session);
                                        $set('number_of_repetition', $exercise->number_of_repetition);
                                        $set('rest', $exercise->rest);
                                    }
                                }),
                            Section::make('exercise_details')
                                ->schema([
                                    TextInput::make('number_of_session')
                                        ->suffix(__('forms.fields.session'))
                                        ->required(),
                                    TextInput::make('number_of_repetition')
                                        ->suffix(__('forms.fields.time'))
                                        ->required(),
                                    TextInput::make('rest')
                                        ->suffix(__('forms.fields.minute'))
                                        ->required(),
                                ])
                                ->hidden(fn (Get $get) => $get('exercise_id') === null),
                        ])
                        ->defaultItems(1),
                ])->columnSpan(1),
        ];

        return $schema;

    }
}
