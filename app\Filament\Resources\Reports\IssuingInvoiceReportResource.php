<?php

namespace Tasawk\Filament\Resources\Reports;

use <PERSON><PERSON>han<PERSON>alleh\FilamentShield\Contracts\HasShieldPermissions;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use pxlrbt\FilamentExcel\Exports\ExcelExport;
use Tasawk\Filament\Resources\Reports\IssuingInvoiceReportResource\Pages;
use Tasawk\Models\Reports\IssuingInvoiceReport;
use Tasawk\Traits\Filament\HasTranslationLabel;

class IssuingInvoiceReportResource extends Resource implements HasShieldPermissions
{
    use HasTranslationLabel, Translatable;

    protected static ?string $model = IssuingInvoiceReport::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-currency-pound';

    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('id')->sortable()->label(__('forms.fields.index')),
                TextColumn::make('order_number')
                    ->copyable()
                    ->searchable(query: fn ($query, $search) => $query->where('id', '=', substr($search, 1)))
                    ->label(__('forms.fields.id')),
                TextColumn::make('meta_data.paid_at')
                    ->label(__('forms.fields.paid_at'))
                    ->formatStateUsing(function (IssuingInvoiceReport $record) {
                        return (new \DateTime($record->meta_data['paid_at']))->format('Y-m-d H:i:s');
                    }),
                TextColumn::make('user.name')
                ->searchable()
                ->label(__('forms.fields.customer_name')),
                TextColumn::make('user.phone')->label(__('forms.fields.phone'))->searchable(),
                TextColumn::make('meta_data.method')->label(__('forms.fields.payment_method'))
                ->searchable(query: fn ($query, $search) => $query->where('meta_data->method', $search)),
                TextColumn::make('meta_data.invoiceId')
                    ->label(__('forms.fields.invoice_id'))
                    ->formatStateUsing(function (IssuingInvoiceReport $record) {
                        return $record->meta_data['invoiceId'];
                    }),
                IconColumn::make('meta_data.invoiceURL')->color('success')->icon('heroicon-o-document-currency-pound')->label(__('forms.fields.incoice_url'))
                    ->url(fn (Model $record) => $record->meta_data['invoiceURL'], '__blank'),
                TextColumn::make('price')
                ->searchable()
                    ->summarize(Tables\Columns\Summarizers\Sum::make()->money('SAR'))
                    ->label(__('forms.fields.total_amount')),
            ])
            ->filters([
                //
            ])
            ->actions([
                // Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    ExportBulkAction::make()->exports([
                        ExcelExport::make('CSV')
                            ->fromTable()
                            ->withFilename(fn () => static::getPluralLabel().'-'.now()->format('Y-m-d'))
                            ->withWriterType(\Maatwebsite\Excel\Excel::XLSX),
                    ]),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListIssuingInvoiceReports::route('/'),
            // 'create' => Pages\CreateIssuingInvoiceReport::route('/create'),
            // 'edit' => Pages\EditIssuingInvoiceReport::route('/{record}/edit'),
        ];
    }

    public static function getNavigationGroup(): ?string
    {
        return __('menu.reports');
    }

    public static function getPermissionPrefixes(): array
    {
        return [
            'view_any',
            'export',
        ];
    }
}