<?php

namespace Tasawk\Models\Meal;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;
use Tasawk\Models\Brand;
use Tasawk\Models\Catalog\Category;
use Tasawk\Models\Catalog\Product;
use Tasawk\Models\Design\Pattern;
use Tasawk\Traits\Publishable;


class MealProduct extends Pivot
{
    use HasFactory;

    use Publishable, SoftDeletes;

    protected $fillable = ['meal_id', 'product_id','value'];

    public $timestamps = false;


    public function meal(){
        return $this->belongsTo(Meal::class);
    }

    public function product(){
        return $this->belongsTo(Product::class)->withTrashed();
    }

}
