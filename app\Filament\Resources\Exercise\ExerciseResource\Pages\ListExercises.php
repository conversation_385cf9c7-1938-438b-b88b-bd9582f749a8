<?php

namespace Tasawk\Filament\Resources\Exercise\ExerciseResource\Pages;

use Tasawk\Filament\Resources\Exercise\ExerciseResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListExercises extends ListRecords
{
    protected static string $resource = ExerciseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
