<?php

namespace Tasawk\Http\Resources\Api\Customer;

use Illuminate\Http\Resources\Json\JsonResource;

class SportsHealthInformationHistoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'question' => $this->question->name,
            'type' => $this->question->type->getLabel(),
            'question_id' => $this->question_id,
            'type_enum' => $this->question->type,
            'value' => $this->values->pluck('value_id'),
            'created_at' => $this?->created_at->format('Y-m-d H:i:s'),

        ];
    }
}