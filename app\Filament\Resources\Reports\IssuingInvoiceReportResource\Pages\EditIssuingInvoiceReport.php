<?php

namespace Tasawk\Filament\Resources\Reports\IssuingInvoiceReportResource\Pages;

use Tasawk\Filament\Resources\Reports\IssuingInvoiceReportResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditIssuingInvoiceReport extends EditRecord
{
    protected static string $resource = IssuingInvoiceReportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\DeleteAction::make(),
        ];
    }
}