<?php

namespace Tasawk\Filament\Resources\Locations\CityResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Str;
use Tasawk\Enum\OrderStatus;
use Tasawk\Models\Order;

class DriversRelationManager extends RelationManager {

    
    protected static string $relationship = 'drivers';

    public function form(Form $form): Form {
        return $form
            ->schema([
                Forms\Components\TextInput::make('id')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table {
        return $table
            ->recordTitleAttribute('id')
            ->heading(__('sections.delegates_list'))
            ->columns([
                TextColumn::make('id')->searchable(),
                TextColumn::make('name')->searchable(),
                TextColumn::make('email')->searchable(),
                TextColumn::make('active')
                    ->badge()
                    
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                // Action::make('change_status')
                //     ->label(__('panel.actions.change_status'))
                //     ->icon('heroicon-o-bolt')
                //     ->disabled(fn(Order $record) => !$record->getAvailableStatus()->count())
                //     ->form([
                //         Select::make('status')
                //             ->options(fn($record) => $record->getAvailableStatus()->pluck('label', 'value')->toArray())
                //             ->required(),
                //     ])
                //     ->visible(fn(Order $record) => auth()->user()->can('update', $record))
                //     ->action(function (array $data, Order $record): void {
                //         if (Str::of($data['status'])->contains("canceled-")) {
                //             $record->cancel(Str::of($data['status'])->after("-"));
                //             return;
                //         }
                //         $record->update(['status' => $data['status']]);
                //     }),
            ]);
    }
}