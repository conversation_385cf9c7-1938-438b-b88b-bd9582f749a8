<?php

namespace Tasawk\Filament\Resources\Exercise\ExerciseResource\Pages;

use Tasawk\Filament\Resources\Exercise\ExerciseResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateExercise extends CreateRecord
{
    use CreateRecord\Concerns\Translatable;
    protected static string $resource = ExerciseResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }
}
