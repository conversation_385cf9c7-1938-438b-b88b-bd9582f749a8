<?php

namespace Tasawk\Http\Requests\Api\Customer;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Tasawk\Models\Subscription\Question;
use Tasawk\Rules\IsValidCoupon;

class CancelSubscriptionRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array {
        return [
            "reason_id" => ['required'],
            'comment' => [Rule::requiredIf($this->reason_id == 0), 'string', 'max:512']
        ];
    }


}