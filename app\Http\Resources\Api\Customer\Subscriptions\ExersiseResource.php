<?php

namespace Tasawk\Http\Resources\Api\Customer\Subscriptions;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ExersiseResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this?->exercise?->name,
            'category' => $this?->exercise?->category?->name,
            'description' => html_entity_decode(strip_tags($this?->exercise?->description)),
            'number_of_repetition' => $this->number_of_repetition,
            'number_of_session' => $this->number_of_session,
            'rest_time' => $this->rest .' '.__('forms.fields.minute'),
            'video' => $this->exercise?->getFirstMediaUrl('video'),
            'image' => $this->exercise?->getFirstMediaUrl(),
            'notes' => $this?->exercise?->notes_text,
        ];
    }
}