<?php

namespace Tasawk\Filament\Resources\Program\ProgramResource\Pages;

use Tasawk\Filament\Resources\Program\ProgramResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPrograms extends ListRecords
{
    protected static string $resource = ProgramResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
