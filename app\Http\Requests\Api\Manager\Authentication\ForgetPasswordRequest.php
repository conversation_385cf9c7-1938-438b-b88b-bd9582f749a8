<?php

namespace Tasawk\Http\Requests\Api\Manager\Authentication;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;
use Tasawk\Models\User;
use Tasawk\Rules\KSAPhoneRule;
use Tasawk\Rules\ManagerPhoneExistRule;

class ForgetPasswordRequest extends FormRequest {

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize() {
        return true;
    }

    protected function prepareForValidation() {

    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules() {
        return [
            'phone' => ['required', new KSAPhoneRule,new ManagerPhoneExistRule()],
        ];
    }

    public function currentUser() {
        $phone = "+" . request()->get('country_code') . request()->get('phone');
        $phone = Str::replace(" ",'', $phone);
        return User::where('phone', $phone)->firstOrFail();
    }


}