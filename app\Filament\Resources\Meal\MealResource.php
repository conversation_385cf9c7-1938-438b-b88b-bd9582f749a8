<?php

namespace Tasawk\Filament\Resources\Meal;

use BezhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Filament\Forms;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\DB;
use Tasawk\Enum\ModelStatus;
use Tasawk\Filament\Resources\Meal\MealResource\Pages;
use Tasawk\Filament\Resources\Meal\MealResource\RelationManagers\MealProductRelationManager;
use Tasawk\Models\Catalog\Option;
use Tasawk\Models\Catalog\Product;
use Tasawk\Models\Meal\CategoryMeal;
use Tasawk\Models\Meal\Meal;
use Tasawk\Traits\Filament\HasTranslationLabel;

class MealResource extends Resource implements HasShieldPermissions
{
    use HasTranslationLabel;
    use Translatable;

    protected static ?string $model = Meal::class;

    protected static ?string $navigationIcon = 'heroicon-o-shopping-cart';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Group::make()->schema([
                    Forms\Components\Section::make('basic_information')->schema([
                        TextInput::make('name')
                            ->required()
                            ->translateLabel(),
                        SpatieMediaLibraryFileUpload::make('image')
                            ->required(),
                        Select::make('category_id')
                            ->required()
                            ->options(CategoryMeal::enabled()->pluck('name', 'id'))
                            ->getOptionLabelFromRecordUsing(fn (CategoryMeal $record) => "{$record->getTranslation('name', 'en')} - {$record->getTranslation('name', 'ar')}")
                            ->searchable(['name->ar', 'name->en']),
                        Toggle::make('status')->default(1)
                            ->label(__('forms.fields.enabled'))
                            ->onColor('success')
                            ->offColor('danger')
                            ->translateLabel(),
                    ]),
                    Forms\Components\Section::make('products')
                        ->schema([
                            Repeater::make('products')
                                ->relationship('products')
                                ->label(__('sections.mealProducts'))
                                ->schema([
                                    Select::make('product_id')
                                        ->label(__('forms.fields.product_title'))
                                        ->reactive()
                                        ->required()
                                        ->options(Product::get()->pluck('title', 'id'))
                                        ->searchable(),
                                    TextInput::make('value')
                                        ->reactive()
                                        ->suffix(fn ($record) => $record?->product?->unit?->name ?? '')
                                        ->default(1)
                                        ->required(),
                                ])->columns(2)
                                ->extraAttributes(['wire:model.defer' => 'products'])
                                ->afterStateUpdated(fn ($state, callable $set) => self::updateTotals($set, $state))
                                ->afterStateHydrated(fn ($state, callable $set) => self::updateTotals($set, $state)),
                        ]),
                ])->columnSpan(3),

                Forms\Components\Group::make()->schema([
                    Forms\Components\Section::make('notes')
                        ->schema([
                            Repeater::make('notes')
                                ->label(__('forms.fields.notes'))
                                ->schema([
                                    TextInput::make('note')
                                        ->label(__('forms.fields.note'))
                                        ->required(),
                                ])->defaultItems(1),
                        ]),
                    Forms\Components\Section::make('totals')
                        ->schema([
                            TextInput::make('sum_of_protien')
                                ->label(__('forms.fields.sum_of_protien'))
                                ->suffix(fn () => Option::where('id', '2')->first()?->unit?->name),
                                // ->disabled(),
                            TextInput::make('sum_of_calories')
                                ->label(__('forms.fields.sum_of_calories'))
                                ->suffix(fn () => Option::where('id', '1')->first()?->unit?->name),
                                // ->disabled(),
                            TextInput::make('sum_of_fat')
                                ->suffix(fn () => Option::where('id', '4')->first()?->unit?->name)
                                ->label(__('forms.fields.sum_of_fat')),
                                // ->disabled(),
                            TextInput::make('sum_of_carbohydrate')
                                ->suffix(fn () => Option::where('id', '3')->first()?->unit?->name)
                                ->label(__('forms.fields.sum_of_carbohydrate')),
                                // ->disabled(),
                            TextInput::make('sum_of_sugars')
                                ->suffix(fn () => Option::where('id', '5')->first()?->unit?->name)
                                ->label(__('forms.fields.sum_of_sugars')),
                                // ->disabled(),
                        ]),
                ])->columnSpan(2),
            ])->columns(5);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('index')->rowIndex(),
                TextColumn::make('id'),
                SpatieMediaLibraryImageColumn::make('image')
                ->getStateUsing(fn (Meal $record) => $record->getFirstMediaUrl('default') ?? ''),
                                TextColumn::make('name'),
                TextColumn::make('category.name'),
                IconColumn::make('status')
                    ->boolean()
                    ->action(
                        Action::make('Active')
                            ->label(fn (Meal $record): string => $record->status ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            // ->disabled(fn (Model $record): bool => ! auth()->user()->can('update', $record))
                            ->requiresConfirmation()
                            ->action(fn (Meal $record) => $record->toggleStatus())
                    ),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                SelectFilter::make('status')
                    ->options(ModelStatus::class),

            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            // MealProductRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMeals::route('/'),
            'create' => Pages\CreateMeal::route('/create'),
            'edit' => Pages\EditMeal::route('/{record}/edit'),
        ];
    }

    public static function getNavigationGroup(): ?string
    {
        return __('sections.meals');
    }

    public static function getPermissionPrefixes(): array
    {
        return [
            'view_any',
            'create',
            'update',
            'delete',
            'delete_any',
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function updateTotals($set, $state)
    {
        $products = $state;

        if (count($products) > 0) {
            $sumOfProtien = 0;
            $sumOfCalories = 0;
            $sumOfFat = 0;
            $sumOfCarbohydrate = 0;
            foreach ($products as $key => $product) {
                $products[$key]['value'] = (int) $product['value'];
                $sumOfProtien += (int) DB::table('product_option')
                    ->where('product_id', $product['product_id'])
                    ->where('option_id', 2)
                    ->sum(DB::raw('CAST(value AS SIGNED)')) * (int) $product['value'];

                $sumOfCalories += (int) DB::table('product_option')
                    ->where('product_id', $product['product_id'])
                    ->where('option_id', 1)
                    ->sum(DB::raw('CAST(value AS SIGNED)')) * (int) $product['value'];

                $sumOfFat += (int) DB::table('product_option')
                    ->where('product_id', $product['product_id'])
                    ->where('option_id', 4)
                    ->sum(DB::raw('CAST(value AS SIGNED)')) * (int) $product['value'];

                $sumOfCarbohydrate += (int) DB::table('product_option')
                    ->where('product_id', $product['product_id'])
                    ->where('option_id', 3)
                    ->sum(DB::raw('CAST(value AS SIGNED)')) * (int) $product['value'];
            }
            $set('sum_of_protien', $sumOfProtien);
            $set('sum_of_calories', $sumOfCalories);
            $set('sum_of_fat', $sumOfFat);
            $set('sum_of_carbohydrate', $sumOfCarbohydrate);

        } else {
            $set('sum_of_protien', 0);
            $set('sum_of_calories', 0);
            $set('sum_of_fat', 0);
            $set('sum_of_carbohydrate', 0);
        }
    }
}
