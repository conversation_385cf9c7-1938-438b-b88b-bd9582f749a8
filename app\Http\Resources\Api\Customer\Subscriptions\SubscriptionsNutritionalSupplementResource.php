<?php

namespace Tasawk\Http\Resources\Api\Customer\Subscriptions;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Tasawk\Models\Catalog\Option;

class SubscriptionsNutritionalSupplementResource extends JsonResource
{
    protected $index;

    public function __construct($resource, $index = null)
    {
        parent::__construct($resource);
        $this->index = $index + 1;
    }

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this?->id,
            'name' => $this?->nutritional_supplement?->name,
            'description' => html_entity_decode(strip_tags($this?->nutritional_supplement?->description)),
            'image' => $this?->nutritional_supplement?->getFirstMediaUrl(),
            'sub_image' => $this?->nutritional_supplement?->getFirstMediaUrl('mockup'),
            'link' => $this?->nutritional_supplement?->link,
            'notes' => $this?->nutritional_supplement?->notes_text,
            'quantity' => $this?->value,
            'sum_of_protien' => $this?->sum_of_protien.' '.Option::where('id', '2')->first()?->unit?->name,
            'sum_of_calories' => $this?->sum_of_calories.' '.Option::where('id', '1')->first()?->unit?->name,
            'sum_of_fat' => $this?->sum_of_fat.' '.Option::where('id', '4')->first()?->unit?->name,
            'sum_of_carbohydrate' => $this?->sum_of_carbohydrate.' '.Option::where('id', '3')->first()?->unit?->name,
        ];
    }
}