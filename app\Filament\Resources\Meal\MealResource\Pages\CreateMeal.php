<?php

namespace Tasawk\Filament\Resources\Meal\MealResource\Pages;

use Tasawk\Filament\Resources\Meal\MealResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateMeal extends CreateRecord
{

    use CreateRecord\Concerns\Translatable;
    protected static string $resource = MealResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }

    // protected function afterCreate(): void
    // {
    //     $data = $this->data[app()->getLocale()];
    //     $meal = $this->record;
    //     $meal->sum_of_protien = $data['sum_of_protien'];
    //     $meal->sum_of_fat = $data['sum_of_fat'];
    //     $meal->sum_of_carbohydrate = $data['sum_of_carbohydrate'];
    //     $meal->sum_of_calories  =    $data['sum_of_calories'];
    //     $meal->save();
    // }
}