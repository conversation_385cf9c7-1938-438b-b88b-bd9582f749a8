<?php

namespace Tasawk\Http\Resources\Api\Customer\Subscriptions;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;
use Tasawk\Enum\SubscriptionStatus;
use Tasawk\Enum\SubscriptionType;
use Tasawk\Http\Resources\Api\Customer\RateResource;
use Tasawk\Settings\GeneralSettings;

class SubscriptionsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $lastRenewal = $this?->renewLog()?->latest()?->first();
        $setting = new GeneralSettings;

        return [
            'id' => $this->id,
            'Subscription_number' => $this->subscription_number,
            'plan' => $this->plan->name,
            'plan_id' => $this->plan_id,
            'type' => $this->type->getLabel(),
            'type_enum' => $this->type->value,
            'created_date' => $this->created_at->format('Y-m-d h:i a'),
            'start_date' => ($this?->programs()->count() == 0 && $this->type == SubscriptionType::NORMAL) ? (__('forms.fields.not_available')) : $this?->start_date?->format('Y-m-d'),
            'end_date' => ($this?->programs()->count() == 0 && $this->type == SubscriptionType::NORMAL) ? (__('forms.fields.not_available')) : $this?->end_date?->format('Y-m-d'),
            'duration' => $this?->duration.' '.__('forms.suffixes.day'),
            'status' => $this->status->getCustomrLabel(),
            'status_enum' => $this->status->value,
            'status_created_at' => $this->StatusHistories()->orderBy('created_at', 'desc')->first() ? $this->StatusHistories()->orderBy('created_at', 'desc')->first()->created_at->format('Y-m-d h:i a') : $this->created_at->format('Y-m-d h:i a'),
            $this->mergeWhen($this->is_renew == 1, [
                'renew_at' => $this?->renew_at?->format('Y-m-d h:i a') ?? '',
            ]),
            'customer' => [
                'id' => $this?->id,
                'gender_enum' => $this?->gender == 'male' ? 0 : 1,
                'gender' => __('forms.fields.'.$this?->gender),
            ],
            'payment' => [
                'url' => route('subscriptions.invoice.download', ['subscription' => $this, 'setting' => $setting]),
                'status' => __('panel.enums.'.$this->payment_status->value),
                'status_code' => Str::headline($this->payment_status->value),
                'method' => asset('visa.png'),
                'paid_at' => isset($this->payment_data['paid_at']) ? Carbon::parse($this->payment_data['paid_at'])->format('Y-m-d h:i a') : '',
                'invoice_reference' => $this?->payment_data['InvoiceReference'] ?? '',
                'invoice_id' => $this?->payment_data['InvoiceId'] ?? '',
                'paid_amount' => $this?->payment_data['focusTransaction']['PaidCurrencyValue'] ?? '',
            ],
            $this->mergeWhen($lastRenewal != null, [
                'renew_details' => [
                    'method' => asset('visa.png'),
                    'total' => ($lastRenewal?->total?->format('f')),
                    'paid_at' => isset($lastRenewal?->payment_data['paid_at']) ? Carbon::parse($lastRenewal?->payment_data['paid_at'])->format('Y-m-d h:i a') : '',
                    'invoice_reference' => $lastRenewal?->payment_data['InvoiceReference'] ?? '',
                    'invoice_id' => $lastRenewal?->payment_data['InvoiceId'] ?? '',
                    'paid_amount' => $lastRenewal?->payment_data['focusTransaction']['PaidCurrencyValue'] ?? '',
                    'url' => route('subscriptions.invoice.download', ['subscription' => $this, 'setting' => $setting]),
                ],
            ]),
            $this->mergeWhen($this->cancellation()->exists(), [
                'cancellation' => [
                    'reason' => $this?->cancellation?->reason_id == 0 ? $this?->cancellation?->note : $this?->cancellation?->reason?->name,
                    'date' => $this?->cancellation?->created_at->format('Y-m-d H:i'),
                ],
            ]),
            $this->mergeWhen($this?->rated(), [
                'rate' => RateResource::make($this?->rate),
            ]),
            'totals' => [
                'items_total_with_options' => 'SAR '.number_format($this->cart_details['0']['items_total_with_options'], 2),
                'subtotal' => 'SAR '.number_format($this->cart_details['0']['subtotal'], 2),
                'coupon_discount' => 'SAR '.number_format($this->cart_details['0']['coupon_discount'], 2),
                'total' => 'SAR '.number_format($this->cart_details['0']['total'], 2),
            ],
            'can' => [
                'cancel' => $this?->canCancel(),
                'show_program' => ($this?->program_id != null && $this->status != SubscriptionStatus::EXPIRED) ? true : false,
                'extend_Subscription' => ($this->status != SubscriptionStatus::EXPIRED) ? true : false,
                'renew_Subscription' => ($this->status == SubscriptionStatus::EXPIRED) ? true : false,
                'rate' => $this?->canRate(),
                $this->mergeWhen($this->type == SubscriptionType::SCHEDULED, [
                    'complete' => (
                        $this->is_completed == 0
                        && $this->start_date <= Carbon::now()->addDays(5)
                        && ! in_array($this->status, [SubscriptionStatus::CANCELED, SubscriptionStatus::EXPIRED])
                    ) ? true : false,                ]),

            ],

        ];
    }
}