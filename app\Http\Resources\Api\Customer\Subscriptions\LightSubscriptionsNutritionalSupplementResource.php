<?php

namespace Tasawk\Http\Resources\Api\Customer\Subscriptions;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Tasawk\Models\Catalog\Option;

class LightSubscriptionsNutritionalSupplementResource extends JsonResource
{
    protected $index;

    public function __construct($resource, $index = null)
    {
        parent::__construct($resource);
        $this->index = $index + 1;
    }

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'index' => $this->index,
            'id' => $this?->id,
            'name' => $this?->nutritional_supplement?->name,
            'quantity' => $this?->value,
            'image' => $this?->nutritional_supplement?->getFirstMediaUrl(),
            'sub_image' => $this?->nutritional_supplement?->getFirstMediaUrl('mockup'),
            'link' => $this?->nutritional_supplement?->link,
        ];
    }
}