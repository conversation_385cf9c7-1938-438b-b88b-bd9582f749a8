<?php

namespace Tasawk\Filament\Resources\Subscription\SubscriptionResource\Pages;

use <PERSON>zhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Actions\Action;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification as FilamentNotification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Database\Eloquent\Model;
use Tasawk\Enum\NormalSubscriptionStatus;
use Tasawk\Enum\ScheduledSubscriptionStatus;
use Tasawk\Enum\SubscriptionStatus;
use Tasawk\Enum\SubscriptionType;
use Tasawk\Filament\Resources\Subscription\SubscriptionResource;
use Tasawk\Models\Catalog\Option;
use Tasawk\Models\Subscription\Subscription;
use Tasawk\Models\SubscriptionCarb;

class ViewSubscription extends ViewRecord
{
    // use HasPageShield;
    protected static string $resource = SubscriptionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('divide-calories')
                ->label(__('forms.fields.divide_calories'))
                ->icon('heroicon-o-divide')
                ->color('success')
                ->form([
                    Section::make('options')
                        ->schema(static::productOptions())
                        ->heading(__('forms.fields.options_heading'))
                ])
                ->visible(fn(Subscription $record) => ! in_array($record->status, [SubscriptionStatus::CANCELED, SubscriptionStatus::EXPIRED]) && count($record->carbs()->get()) == 0)
                ->action(function (array $data, Subscription $record, $action): void {
                    foreach ($data['options'] as $option_id => $value) {
                        SubscriptionCarb::create([
                            'subscription_id' => $record->id,
                            'option_id' => $option_id,
                            'percentage' => $value,
                        ]);
                    }

                    FilamentNotification::make()
                        ->success()
                        ->title(__('panel.messages.success'))
                        ->body(__('panel.messages.calories_divided'))
                        ->persistent()
                        ->send();
                }),
            Action::make('add_notes')
                ->label(__('forms.fields.add_notes'))
                ->icon('heroicon-o-pencil-square')
                ->color('success')
                ->form([
                    Textarea::make('notes')
                        ->label(__('forms.fields.notes'))
                        ->formatStateUsing(fn($record) => $record?->notes ?? '')
                        ->rows(10)
                        ->required()
                ])
                ->visible(fn(Subscription $record) => ! in_array($record->status, [SubscriptionStatus::CANCELED, SubscriptionStatus::EXPIRED]))
                ->action(function (array $data, Subscription $record, $action): void {
                    $record->update(['notes' => $data['notes']]);
                    FilamentNotification::make()
                        ->success()
                        ->title(__('panel.messages.success'))
                        ->body(__('panel.messages.notes_added'))
                        ->persistent()
                        ->send();
                }),
            Action::make('change-status')
                ->label(__('forms.fields.change_status'))
                ->icon('heroicon-o-bolt')
                ->color('primary')
                ->form([
                    Select::make('status')
                        ->formatStateUsing(fn($record) => $record->status->value)
                        ->options(fn($record) => $record->type == SubscriptionType::NORMAL ? NormalSubscriptionStatus::class : ScheduledSubscriptionStatus::class),
                ])
                ->visible(fn(Subscription $record) => ! in_array($record->status, [SubscriptionStatus::CANCELED, SubscriptionStatus::EXPIRED]))
                ->action(function (array $data, Subscription $record, $action): void {

                    $record->update(['status' => $data['status']]);
                    $record->StatusHistories()->create([
                        'status' => $data['status'],
                        'subscription_id' => $record->id,
                    ]);
                    FilamentNotification::make()
                        ->success()
                        ->title(__('panel.messages.success'))
                        ->body(__('panel.messages.change_status'))
                        ->persistent()
                        ->send();
                }),
            Action::make('assign-program')
                ->label(__('forms.fields.assign-program'))
                ->icon('heroicon-o-plus-circle')
                ->color('success')
                ->url(fn($record) => SubscriptionResource::getUrl('assign-program', [$record->id]))
                ->modalWidth('7xl')
                ->visible(
                    fn(Subscription $record) => ! in_array($record->status, [SubscriptionStatus::CANCELED, SubscriptionStatus::EXPIRED]) && count($record->carbs()->get()) > 0
                ),
            Action::make('show_invoice')
                ->label(__('forms.fields.show_invoice'))
                ->icon('heroicon-o-eye')
                ->color('danger')
                ->url(fn(Model $record) => $record?->payment_data['invoiceURL'], '_blank')
                ->visible(fn($record) => $record?->payment_data != null),

            Action::make('download_invoice')
                ->label(__('forms.fields.download_invoice'))
                ->icon('heroicon-o-arrow-down-tray')
                ->color('success')
                ->action(function (array $data, Subscription $record, $action) {
                    return redirect()->to(route('subscriptions.invoice.download', $record));
                })
                ->visible(fn($record) => $record?->payment_status->value == 'paid'),

        ];
    }

    public static function productOptions()
    {
        $schema = [];
        foreach (Option::where('mapper_key',null)->latest()->get() as $option) {
            $schema[] = TextInput::make("options.{$option->id}")
                ->required()
                ->label($option->name)
                ->suffix('%');
        }
        return  $schema;
    }
}
