<?php

namespace Tasawk\Filament\Resources\Subscription\QuestionResource\Pages;

use Tasawk\Filament\Resources\Subscription\QuestionResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Resources\Pages\CreateRecord\Concerns\Translatable;
use Filament\Forms\Form;


class CreateQuestion extends CreateRecord
{

    use Translatable,QuestionResource\Schema\QuestionSchema;

    protected static string $resource = QuestionResource::class;

    protected function getHeaderActions(): array {

        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }
    public function form(Form $form): Form {
        return $form->schema($this->schema());
    }
}
