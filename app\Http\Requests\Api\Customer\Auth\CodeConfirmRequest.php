<?php

namespace Tasawk\Http\Requests\Api\Customer\Auth;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;
use Tasawk\Models\User;
use Tasawk\Rules\IsValidVerificationCodeRule;
use Tasawk\Rules\KSAPhoneRule;
use Tasawk\Rules\PhoneNumber;


class CodeConfirmRequest extends FormRequest {

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize() {
        return true;
    }



    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules() {
        return [

            'code' => ['required', 'numeric','digits:4', new IsValidVerificationCodeRule()],
            'phone' => [
                'required',
                // 'exists:users',
                'regex:/^\d{8,15}$/',
                new PhoneNumber($this->input('country_code')),
            ],
            'country_code' => ['required', 'string', 'regex:/^\d{1,4}$/'],
        ];
    }

    public function currentUser() {
        $phone = "+" . request()->get('country_code') . request()->get('phone');
        $phone = Str::replace(" ",'', $phone);
        return User::where('phone', $phone)->firstOrFail();
    }
}