<?php

namespace Tasawk\Http\Requests\Api\Customer;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Tasawk\Enum\Catalog\OptionTypes;
use Tasawk\Models\Subscription\Question;
use Tasawk\Rules\IsValidCoupon;

class ProfileHistoryQuestionRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'questions' => ['required', 'array'],
            'questions.*.question_id' => ['required', 'integer', 'exists:questions,id'],
            'questions.*.value' => [
                'required',
                function ($attribute, $value, $fail) {
                    $questionId = $this->input(str_replace('.value', '.question_id', $attribute));
                    $question = Question::find($questionId);
                    if (! $question) {
                        $fail(__('validation.exists', ['attribute' => __('validation.attributes.question_id')]));

                        return;
                    }
                    switch ($question->type) {
                        case OptionTypes::TEXTAREA:
                            if (empty($value)) {
                                $fail(__('validation.required', ['attribute' => __('validation.attributes.value')]));
                            }
                            break;
                        case OptionTypes::RADIO:
                            if (! in_array($value, $question->values->pluck('id')->toArray())) {
                                $fail(__('validation.in', ['attribute' => __('validation.attributes.value')]));
                            }
                            break;
                        case OptionTypes::CHECKBOX:
                            $values = explode(',', $value);
                            $validValues = $question->values->pluck('id')->toArray();
                            foreach ($values as $v) {
                                if (! in_array($v, $validValues)) {
                                    $fail(__('validation.in', ['attribute' => __('validation.attributes.value')]));
                                }
                            }
                            break;
                        case OptionTypes::TIME:
                            if (! preg_match('/^([01]\d|2[0-3]):([0-5]\d)$/', $value)) {
                                $fail(__('validation.regex', ['attribute' => __('validation.attributes.value')]));
                            }
                            break;
                        case OptionTypes::DATE:
                            if (! preg_match('/^\d{4}-\d{2}-\d{2}$/', $value)) {
                                $fail(__('validation.regex', ['attribute' => __('validation.attributes.value')]));
                            }
                            break;
                        case OptionTypes::DATETIME:
                            if (! preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $value)) {
                                $fail(__('validation.regex', ['attribute' => __('validation.attributes.value')]));
                            }
                            break;
                        default:
                            break;
                    }
                },
            ],
        ];
    }


    public function messages()
    {
        $optionIds = $this->input('questions.*.question_id');
        $optionNames = Question::whereIn('id', $optionIds)->get();
        $messages = [
            'questions.question_id.required' => __('validation.required', ['attribute' => __('validation.attributes.question_id')]),
            'questions.question_id.exists' => __('validation.exists', ['attribute' => __('validation.attributes.question_id')]),
        ];

        foreach ($optionNames as $value) {
            $messages['questions.*.value.required'] = __('validation.required', ['attribute' => $value->name]);
        }
        return $messages;
    }
}