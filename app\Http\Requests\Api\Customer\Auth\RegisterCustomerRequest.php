<?php

namespace Tasawk\Http\Requests\Api\Customer\Auth;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Password;
use Tasawk\Models\User;
use Tasawk\Rules\PhoneNumber;

class RegisterCustomerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'full_name' => ['required', 'string', 'min:3', 'max:40'],

            'phone' => [
                'required',
                'regex:/^\d{8,15}$/',
                function ($attribute, $value, $fail) {
                    $fullPhone = '+'.$this->input('country_code').$value;
                    $exists = User::where('phone', $fullPhone)->exists();
                    if ($exists) {
                        $fail(__('validation.api.customer_exists'));
                    }
                },
                new PhoneNumber($this->input('country_code')),
            ],
            'country_code' => ['required', 'string', 'regex:/^\d{1,4}$/'],
            'email' => ['nullable', 'email', 'unique:users'],
            'password' => ['required',
                Password::min(8)
                    ->mixedCase()
                    ->letters()
                    ->numbers()
                    ->symbols(),
            ],
            'zone_id' => ['required', 'exists:zones,id'],
            'city_id' => ['required', 'exists:cities,id'],
            'gender' => ['required', 'in:0,1'],
            'password_confirmation' => ['required', 'same:password'],
            'device_token' => ['required'],
            'is_approved_conditions' => ['required', 'in:1'],

        ];
    }
}