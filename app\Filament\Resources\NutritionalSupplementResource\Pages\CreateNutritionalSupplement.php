<?php

namespace Tasawk\Filament\Resources\NutritionalSupplementResource\Pages;

use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Resources\Pages\CreateRecord\Concerns\Translatable;
use Tasawk\Filament\Resources\NutritionalSupplementResource;

class CreateNutritionalSupplement extends CreateRecord
{
    use Translatable;

    protected function getHeaderActions(): array
    {

        return [
            Actions\LocaleSwitcher::make(),
        ];
    }

    protected static string $resource = NutritionalSupplementResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }


    protected function afterCreate(): void
    {
        $data = $this->data['options'];
        $product = $this->record;
        $options = [];
        foreach ($data as $key => $value) {
            $options[$key] = ['value' => $value];
        }
        $product->options()->sync($options);
    }
}