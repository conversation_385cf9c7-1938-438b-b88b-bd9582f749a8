<?php

namespace Tasawk\Filament\Resources\Program;

use <PERSON><PERSON>hanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Tasawk\Enum\ModelStatus;
use Tasawk\Filament\Resources\Program\ProgramResource\Pages;
use Tasawk\Models\Catalog\Option;
use Tasawk\Models\Exercise\Exercise;
use Tasawk\Models\Meal\Meal;
use Tasawk\Models\Program\Program;
use Tasawk\Traits\Filament\HasTranslationLabel;

class ProgramResource extends Resource implements HasShieldPermissions
{
    use HasTranslationLabel;
    use Translatable;

    protected static ?string $model = Program::class;

    protected static ?int $navigationSort = 3;

    protected static ?string $navigationIcon = 'heroicon-o-clipboard-document';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([

                Wizard::make(fn (Get $get, $livewire, $component, Request $request) => [
                    Wizard\Step::make('basic_information')
                        ->label(__('sections.basic_information'))
                        ->schema([
                            TextInput::make('name')
                                ->required()
                                ->translateLabel(),
                            TextInput::make('duration')
                                ->live()
                                ->default(1)
                                ->suffix(__('forms.suffixes.day'))
                                ->required()
                                ->translateLabel(),
                        ]),
                    ...self::buildDaysWizardTabs($get('duration')),

                ])->startOnStep(1),
            ])
            ->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('index')->rowIndex(),
                TextColumn::make('id'),
                TextColumn::make('name')->searchable(),
                TextColumn::make('duration')->searchable(),
                IconColumn::make('status')
                    ->boolean()
                    ->action(
                        Action::make('Active')
                            ->label(fn (Program $record): string => $record->status ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            // ->disabled(fn (Model $record): bool => ! auth()->user()->can('update', $record))
                            ->requiresConfirmation()
                            ->action(fn (Program $record) => $record->toggleStatus())
                    ),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                SelectFilter::make('status')
                    ->options(ModelStatus::class),

            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPrograms::route('/'),
            'create' => Pages\CreateProgram::route('/create'),
            'edit' => Pages\EditProgram::route('/{record}/edit'),
        ];
    }

    public static function getNavigationGroup(): ?string
    {
        return __('sections.meals');
    }

    public static function getPermissionPrefixes(): array
    {
        return [
            'view_any',
            'create',
            'update',
            'delete',
            'delete_any',
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function buildDaysWizardTabs($days): array
    {
        $schema = [];
        if ($days <= 0) {
            return $schema;
        }
        foreach (range(1, $days) as $day) {

            $schema[] = Wizard\Step::make($day)
                ->icon('heroicon-m-shopping-bag')
                ->schema([
                    ...self::buildMealsRepeater($day),
                    ...self::buildExerciseRepeater($day),

                ])->statePath('days.'.$day)
                ->columns(2);
        }

        return $schema;
    }

    public static function buildMealsRepeater($day)
    {
        return [
            Section::make('meals')
                ->label(__('menu.meals'))
                ->schema([
                    Repeater::make('meals')
                        ->label('')
                        ->relationship('meals', fn ($query) => $query->where('sort', $day))
                        ->schema([
                            Hidden::make('sort')->default($day),
                            Select::make('meal_id')
                                ->required()
                                ->reactive()
                                ->options(Meal::enabled()->pluck('name', 'id'))
                                ->searchable(['name->ar', 'name->en'])
                                ->afterStateUpdated(function ($state, callable $set, callable $get) {
                                    $meal = Meal::with('products')->find($state);
                                    if ($meal) {
                                        foreach ($meal->products as $product) {
                                            $set("products.{$product->id}.product_name", $product->product->title);
                                            $set("products.{$product->id}.product_id", $product->product->id);
                                            $set("products.{$product->id}.value", $product->value);
                                        }
                                        self::computeTotals($set, $meal);
                                    }
                                }),
                            Repeater::make('products')
                                ->addable(false)
                                ->schema([
                                    TextInput::make('product_name')
                                        ->formatStateUsing(fn ($record) => $record?->product?->title)
                                        ->label(__('forms.fields.product_title'))
                                        ->disabled(),
                                    Hidden::make('product_id')
                                        ->default(fn ($record) => $record->product_id)
                                        ->required(),

                                    TextInput::make('value')
                                        ->label(__('forms.fields.quantity'))
                                        ->numeric()
                                        ->suffix(fn ($record) => $record?->product?->unit?->name ?? '')
                                        // ->afterStateHydrated(fn ($state, callable $set) => self::updateTotals($set, $state))
                                        ->required(),
                                ])
                                ->defaultItems(0)
                                ->relationship('products')
                                ->columns(2)
                                // ->afterStateUpdated(fn ($state, callable $set) => self::updateTotals($set, $state))
                                // ->afterStateHydrated(fn ($state, callable $set) => self::updateTotals($set, $state))
                                ->hidden(fn ($get) => $get('meal_id') === null),
                            Section::make('totals')
                                ->schema([
                                    TextInput::make('sum_of_protien')
                                        ->label(__('forms.fields.sum_of_protien'))
                                        ->suffix(fn () => Option::where('id', '2')->first()?->unit?->name),
                                    // ->disabled(),
                                    TextInput::make('sum_of_calories')
                                        ->label(__('forms.fields.sum_of_calories'))
                                        ->suffix(fn () => Option::where('id', '1')->first()?->unit?->name),
                                    // ->disabled(),
                                    TextInput::make('sum_of_fat')
                                        ->suffix(fn () => Option::where('id', '4')->first()?->unit?->name)
                                        ->label(__('forms.fields.sum_of_fat')),
                                    // ->disabled(),
                                    TextInput::make('sum_of_carbohydrate')
                                        ->suffix(fn () => Option::where('id', '3')->first()?->unit?->name)
                                        ->label(__('forms.fields.sum_of_carbohydrate')),
                                    TextInput::make('sum_of_sugars')
                                        ->suffix(fn () => Option::where('id', '5')->first()?->unit?->name)
                                        ->label(__('forms.fields.sum_of_sugars')),
                                    // ->disabled(),
                                ])
                                ->reactive()
                                ->hidden(fn ($get) => $get('meal_id') === null),
                        ])
                        ->columns(1),

                ])
                ->columnSpan(1),
        ];
    }

    public static function computeTotals(callable $set, $meal)
    {
        $set('sum_of_protien', $meal->sum_of_protien);
        $set('sum_of_calories', $meal->sum_of_calories);
        $set('sum_of_fat', $meal->sum_of_fat);
        $set('sum_of_carbohydrate', $meal->sum_of_carbohydrate);
        $set('sum_of_sugars', $meal->sum_of_sugars);
    }

    public static function buildExerciseRepeater($day): array
    {
        $schema = [
            Section::make('exercises')
                ->label(__('menu.exercises'))
                ->schema([
                    Repeater::make('exercises')
                        ->relationship('exercises', fn ($query) => $query->where('sort', $day))
                        ->label('')
                        ->schema([
                            Hidden::make('sort')->default($day),
                            Select::make('exercise_id')
                                ->required()
                                ->options(Exercise::enabled()->pluck('name', 'id'))
                                ->getOptionLabelFromRecordUsing(fn (Exercise $record) => "{$record->getTranslation('name', 'en')} - {$record->getTranslation('name', 'ar')}")
                                ->searchable(['name->ar', 'name->en'])
                                ->reactive()
                                ->afterStateUpdated(function ($state, callable $set) {
                                    $exercise = Exercise::find($state);
                                    if ($exercise) {
                                        $set('number_of_session', $exercise->number_of_session);
                                        $set('number_of_repetition', $exercise->number_of_repetition);
                                        $set('rest', $exercise->rest);
                                    }
                                }),
                            Section::make('exercise_details')
                                ->schema([
                                    TextInput::make('number_of_session')
                                        ->suffix(__('forms.fields.session'))
                                        ->required(),
                                    TextInput::make('number_of_repetition')
                                        ->suffix(__('forms.fields.time'))
                                        ->required(),
                                    TextInput::make('rest')
                                        ->suffix(__('forms.fields.minute'))
                                        ->required(),
                                ])
                                ->hidden(fn (Get $get) => $get('exercise_id') === null),
                        ])
                        ->defaultItems(1),
                ])->columnSpan(1),
        ];

        return $schema;

    }

    public static function updateTotals($set, $state)
    {
        dd($state);
        $products = $state;

        if (count($products) > 0) {
            $sumOfProtien = 0;
            $sumOfCalories = 0;
            $sumOfFat = 0;
            $sumOfCarbohydrate = 0;
            foreach ($products as $key => $product) {
                $products[$key]['value'] = (int) $product['value'];
                $sumOfProtien += (int) DB::table('product_option')
                    ->where('product_id', $product['product_id'])
                    ->where('option_id', 2)
                    ->sum(DB::raw('CAST(value AS SIGNED)')) * (int) $product['value'];

                $sumOfCalories += (int) DB::table('product_option')
                    ->where('product_id', $product['product_id'])
                    ->where('option_id', 1)
                    ->sum(DB::raw('CAST(value AS SIGNED)')) * (int) $product['value'];

                $sumOfFat += (int) DB::table('product_option')
                    ->where('product_id', $product['product_id'])
                    ->where('option_id', 4)
                    ->sum(DB::raw('CAST(value AS SIGNED)')) * (int) $product['value'];

                $sumOfCarbohydrate += (int) DB::table('product_option')
                    ->where('product_id', $product['product_id'])
                    ->where('option_id', 3)
                    ->sum(DB::raw('CAST(value AS SIGNED)')) * (int) $product['value'];
            }
            $set('sum_of_protien', $sumOfProtien);
            $set('sum_of_calories', $sumOfCalories);
            $set('sum_of_fat', $sumOfFat);
            $set('sum_of_carbohydrate', $sumOfCarbohydrate);

        } else {
            $set('sum_of_protien', 0);
            $set('sum_of_calories', 0);
            $set('sum_of_fat', 0);
            $set('sum_of_carbohydrate', 0);
        }
    }
}
