<?php

namespace Tasawk\Filament\Resources\Program\ProgramResource\Pages;

use Tasawk\Filament\Resources\Program\ProgramResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditProgram extends EditRecord {
    use EditRecord\Concerns\Translatable;

    protected static string $resource = ProgramResource::class;

    protected function getHeaderActions(): array {
        return [
            Actions\DeleteAction::make(),
            Actions\LocaleSwitcher::make(),

        ];
    }

    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }


}