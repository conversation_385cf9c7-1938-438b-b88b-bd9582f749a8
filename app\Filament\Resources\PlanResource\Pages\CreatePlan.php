<?php

namespace Tasawk\Filament\Resources\PlanResource\Pages;

use Tasawk\Filament\Resources\PlanResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreatePlan extends CreateRecord
{
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = PlanResource::class;
    protected function getHeaderActions(): array {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }
}
