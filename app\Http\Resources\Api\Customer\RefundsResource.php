<?php

namespace Tasawk\Http\Resources\Api\Customer;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class RefundsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this?->id,
            'subscription_number' => $this?->subscription_number,
            'paid_at' =>$this?->created_at->format('Y-m-d H:i:s'),
            'invoice_reference' =>  $this?->payment_data['InvoiceReference'] ?? '',
            'invoice_id' =>  $this?->payment_data['InvoiceId'] ?? '',
            'method' =>  asset('visa.png') ,
            'url' => route('subscriptions.invoice.download',$this),
            'invoice_url' =>  $this?->payment_data['invoiceURL'] ?? '',
            // 'total' => 'SAR '.$this?->total->formatByDecimal() ?? '',
            'status' => [
                'enum' => $this?->payment_status,
                'label' => $this?->payment_status?->getLabel(),
            ],
            'created_at' => $this?->created_at->format('Y-m-d H:i:s'),

        ];
    }
}