<?php

namespace Tasawk\Api\V1\Customer;

use Api;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Notification;
use Tasawk\Actions\Customer\CustomerHasRightsToLogin;
use Tasawk\Actions\Customer\RegisterCustomer;
use Tasawk\Actions\Shared\Authentication\ForgetPassword;
use Tasawk\Actions\Shared\Authentication\SendVerificationCode;
use Tasawk\Actions\Shared\Authentication\UpdateUserPassword;
use Tasawk\Actions\Shared\Authentication\UpdateUserToken;
use Tasawk\Actions\Shared\Authentication\VerifyUserAccount;
use Tasawk\Api\Core;
use Tasawk\Http\Requests\Api\Customer\Auth\CodeConfirmRequest;
use Tasawk\Http\Requests\Api\Customer\Auth\ForgetPasswordRequest;
use Tasawk\Http\Requests\Api\Customer\Auth\LoginRequest;
use Tasawk\Http\Requests\Api\Customer\Auth\RegisterCustomerRequest;
use Tasawk\Http\Requests\Api\Customer\Auth\ResetPasswordRequest;
use Tasawk\Http\Requests\Api\Customer\Auth\SendOTPRequest;
use Tasawk\Http\Requests\Api\Customer\Auth\VerifyAccountRequest;
use Tasawk\Http\Resources\Api\Customer\CustomerResource;
use Tasawk\Models\Customer;
use Tasawk\Models\User;
use Tasawk\Notifications\Customer\CustomerRegisteredNotification;

class AuthServices
{
    public function login(LoginRequest $request)
    {
        $phone = "+" . request()->get('country_code') . request()->get('phone');
        $phone = Str::replace(" ",'', $phone);
        $request->merge(['phone' => $phone]);
        if (! Auth::once($request->only('phone', 'password'))) {
            return Api::isError(__('validation.api.invalid_credentials'))->setErrors(['credentials' => __('validation.api.invalid_credentials')]);
        }
        CustomerHasRightsToLogin::run();
        UpdateUserToken::run(auth()->user());
        return Api::isOk(__('Customer information'))->setData(new CustomerResource(auth()->user()));
    }

    public function verifySMSCode(CodeConfirmRequest $request): Core
    {
        return Api::isOk(__('Correct Verification code'));

    }

    public function register(RegisterCustomerRequest $request)
    {
        $customer = RegisterCustomer::run(
            $request->input('full_name'),
            $request->input('phone'),
            $request->input('country_code'),
            $request->input('email', null),
            $request->input('password'),
            $request->input('zone_id'),
            $request->input('city_id'),
            $request->input('gender'),
            $request->input('device_token')
        );
        SendVerificationCode::run($customer);
        Notification::send(User::whereHas('roles', fn ($q) => $q->where('name', 'super_admin'))->first(), new CustomerRegisteredNotification);

        // Determine message based on country
        $isSaudiArabia = $this->isSaudiArabiaUser($customer->phone);
        $message = $isSaudiArabia
            ? __('Registration process succeed, SMS Code sent')
            : __('Registration process succeed, Verification code sent to your email');

        return Api::isOk($message);
    }

    public function verify(VerifyAccountRequest $request)
    {
        VerifyUserAccount::run($request->currentUser());

        return Api::isOk(__('Verified,User information'))->setData(new CustomerResource($request->currentUser()));

    }

    public function forgetPassword(ForgetPasswordRequest $request): Core
    {
        ForgetPassword::run($request->currentUser());

        return Api::isOk(__('SMS code sent'));

    }

    public function resetPassword(ResetPasswordRequest $request): Core
    {
        UpdateUserPassword::run($request->currentUser(), $request->get('password'));

        return Api::isOk(__('User information'))->setData(new CustomerResource($request->currentUser()));

    }

    public function sendOTP(SendOTPRequest $request): Core
    {
        $code = 1234;
        auth()->user()->verificationCodes()->delete();
        auth()->user()->verificationCodes()->create(['phone' => $request->get('phone'), 'code' => $code]);

        return Api::isOk(__('OTP sent'))->setData([]);

    }

    /**
     * Check if the user is from Saudi Arabia based on phone number
     *
     * @param string $phoneNumber
     * @return bool
     */
    private function isSaudiArabiaUser($phoneNumber)
    {
        // Remove any spaces and special characters
        $cleanPhone = Str::replace([' ', '+', '-'], '', $phoneNumber);

        // Check if phone starts with 966 (Saudi Arabia country code)
        return Str::startsWith($cleanPhone, '966');
    }
}
