<?php

namespace Tasawk\Http\Resources\Api\Customer\Subscriptions;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Tasawk\Enum\SubscriptionType;
use Tasawk\Settings\GeneralSettings;

class LightSubscriptionsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $cart = $this->as_cart;
        $settings = new GeneralSettings;

        return [
            'id' => $this->id,
            'Subscription_number' => $this->subscription_number,
            'plan' => $this->plan->name,
            'plan_id' => $this->plan_id,
            'type' => $this->type->getLabel(),
            'type_enum' => $this->type->value,
            $this->mergeWhen($this->type == SubscriptionType::SCHEDULED, [
                'start_date' => $this?->start_date?->format('Y-m-d'),
            ]),
            'created_date' => $this->created_at->format('Y-m-d h:i a'),
            'start_date' => ($this?->programs()->count() == 0 && $this->type == SubscriptionType::NORMAL) ? (__('forms.fields.not_available')) : $this?->start_date?->format('Y-m-d'),
            'end_date' => ($this?->programs()->count() == 0 && $this->type == SubscriptionType::NORMAL) ? (__('forms.fields.not_available')) : $this?->end_date?->format('Y-m-d'),
            'duration' => $this?->duration.' '.__('forms.suffixes.day'),
            'status' => $this->status->getCustomrLabel(),
            'status_enum' => $this->status->value,
            'customer' => [
                'id' => $this?->id,
                'gender_enum' => $this?->gender == 'male' ? 0 : 1,
                'gender' => __('forms.fields.'.$this?->gender),
            ],
            $this->mergeWhen($this->type == SubscriptionType::SCHEDULED, [
                'can_complete' => ($this->is_completed == 0 && $this->start_date <= Carbon::now()->addDays(5)) ? true : false,
            ]),
        ];
    }
}