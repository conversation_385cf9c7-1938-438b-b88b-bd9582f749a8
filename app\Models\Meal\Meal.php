<?php

namespace Tasawk\Models\Meal;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;
use Tasawk\Models\Brand;
use Tasawk\Models\Catalog\Category;
use Tasawk\Models\Catalog\Product;
use Tasawk\Models\Design\Pattern;
use Tasawk\Traits\Publishable;


class Meal extends Model implements HasMedia
{
    use HasFactory;

    use HasTranslations,InteractsWithMedia, Publishable, SoftDeletes;

    protected $fillable = ['name', 'status','category_id','notes','sum_of_protien','sum_of_fat','sum_of_calories','sum_of_carbohydrate','sum_of_sugars'];
    public $translatable = ['name','notes'];
    protected $casts = [
        'notes' => 'array',
        'status' => 'boolean',
    ];

    public function getImageAttribute() {
        return $this->getFirstMediaUrl();
    }


    public function category(): BelongsTo {
        return $this->belongsTo(Category::class);
    }

    public function products() {
       return $this->hasMany(MealProduct::class)->withTrashed();
    }

    public function getNotesTextAttribute()
    {
        $notes = [];
        $notesArray = is_array($this->notes) ? $this->notes : json_decode($this->notes, true);

        if (is_array($notesArray)) {
            foreach ($notesArray as $note) {
                if (isset($note['note'])) {
                    $notes[] = $note['note'];
                }
            }
        }

        return $notes;
    }



}
